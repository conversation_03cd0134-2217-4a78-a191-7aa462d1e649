package com.airdoc.research.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 扫视能力检测响应DTO
 */
@Data
public class SaccadeAbilityResponseDTO {

    /**
     * 结果ID
     */
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者住院号
     */
    private String inpatientNum;

    /**
     * 患者病例卡号
     */
    private String caseCardNum;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 检测类型
     */
    private String testType;

    /**
     * 检测序列号
     */
    private String testSequence;

    /**
     * 检测日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testDate;

    /**
     * 检测时长(秒)
     */
    private Integer duration;

    /**
     * 检测状态
     */
    private String status;

    /**
     * 检测状态描述
     */
    private String statusDesc;

    /**
     * 目标点序列
     */
    private String targetPoints;

    /**
     * 扫视事件详情
     */
    private String saccadeEvents;

    /**
     * 视线轨迹JSON数据
     */
    private String gazeTrajectoryJson;

    /**
     * 总扫视次数
     */
    private Integer totalSaccades;

    /**
     * 成功扫视次数
     */
    private Integer successfulSaccades;

    /**
     * 准确率(%)
     */
    private BigDecimal accuracyRate;

    /**
     * 平均扫视时间(ms)
     */
    private BigDecimal averageSaccadeTime;

    /**
     * 平均扫视速度(度/秒)
     */
    private BigDecimal saccadeVelocity;

    /**
     * 平均误差距离
     */
    private BigDecimal errorDistance;

    /**
     * 扫视潜伏期(ms)
     */
    private BigDecimal latency;

    /**
     * 峰值速度(度/秒)
     */
    private BigDecimal peakVelocity;

    /**
     * 欠冲率(%)
     */
    private BigDecimal undershootRate;

    /**
     * 过冲率(%)
     */
    private BigDecimal overshootRate;

    /**
     * 注视稳定性评分
     */
    private BigDecimal fixationStability;

    /**
     * 校准参数
     */
    private String calibrationParams;

    /**
     * 环境信息
     */
    private String environmentInfo;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 检测图片URL
     */
    private String imageUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
