//package com.airdoc.research.controller;
//
//import com.airdoc.research.dto.SaccadeAbilityPageQueryDTO;
//import com.airdoc.research.dto.SaccadeAbilityQueryDTO;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//
//import java.math.BigDecimal;
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
///**
// * 扫视能力检测控制器测试
// */
//@SpringBootTest
//@AutoConfigureTestMvc
//public class SaccadeAbilityControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void testPageQuerySaccadeAbilityTests() throws Exception {
//        SaccadeAbilityPageQueryDTO queryDTO = new SaccadeAbilityPageQueryDTO();
//        queryDTO.setCurrent(1);
//        queryDTO.setSize(10);
//        queryDTO.setMinAccuracyRate(new BigDecimal("80.0"));
//        queryDTO.setMaxAccuracyRate(new BigDecimal("100.0"));
//
//        mockMvc.perform(post("/api/movement-test/saccade-ability/page")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(queryDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.data").exists())
//                .andExpect(jsonPath("$.data.current").value(1))
//                .andExpect(jsonPath("$.data.size").value(10));
//    }
//
//    @Test
//    public void testQuerySaccadeAbilityTests() throws Exception {
//        SaccadeAbilityQueryDTO queryDTO = new SaccadeAbilityQueryDTO();
//        queryDTO.setPatientId(1L);
//        queryDTO.setMinTotalSaccades(5);
//        queryDTO.setMaxTotalSaccades(20);
//
//        mockMvc.perform(post("/api/movement-test/saccade-ability/list")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(queryDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.data").isArray());
//    }
//
//    @Test
//    public void testGetSaccadeAbilityTestById() throws Exception {
//        Long testId = 1L;
//
//        mockMvc.perform(get("/api/movement-test/saccade-ability/{id}", testId))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").exists());
//    }
//
//    @Test
//    public void testGetSaccadeAbilityTestByRecordId() throws Exception {
//        Long recordId = 1L;
//
//        mockMvc.perform(get("/api/movement-test/saccade-ability/record/{recordId}", recordId))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").exists());
//    }
//
//    @Test
//    public void testCountSaccadeAbilityTests() throws Exception {
//        SaccadeAbilityQueryDTO queryDTO = new SaccadeAbilityQueryDTO();
//        queryDTO.setMinAccuracyRate(new BigDecimal("70.0"));
//
//        mockMvc.perform(post("/api/movement-test/saccade-ability/count")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(queryDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.data").isNumber());
//    }
//
//    @Test
//    public void testPageQueryWithInvalidParameters() throws Exception {
//        SaccadeAbilityPageQueryDTO queryDTO = new SaccadeAbilityPageQueryDTO();
//        queryDTO.setCurrent(0); // 无效的页码
//        queryDTO.setSize(0);    // 无效的页大小
//
//        mockMvc.perform(post("/api/movement-test/saccade-ability/page")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(queryDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(false));
//    }
//}
