# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*
replay_pid*

### Maven ###
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### Gradle ###
.gradle
**/build/
!src/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Spring Boot ###
*.pid
*.pid.lock

### Application Properties ###
# Keep main application.properties but ignore environment-specific ones
application-local.properties
application-dev.properties
application-prod.properties
application-test.properties

### Database ###
*.db
*.sqlite
*.sqlite3

### Logs ###
logs/
log/
*.log

### Temporary files ###
*.tmp
*.temp
*.swp
*.swo
*~

### OS generated files ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

### Test results ###
TEST-*.xml
test-output/
coverage/
*.lcov

### Custom application files ###
uploads/
temp/
cache/

### Environment variables ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### JRebel ###
rebel.xml

### JProfiler ###
*.jprofile

### YourKit ###
*.yjp

### Editor-based Rest Client ###
.idea/httpRequests

### JetBrains templates ###
**___jb_tmp___

### Backup files ###
*.bak
*.backup

### Documentation ###
HELP.md
