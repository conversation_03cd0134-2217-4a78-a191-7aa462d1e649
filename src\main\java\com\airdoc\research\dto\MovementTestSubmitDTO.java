package com.airdoc.research.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Movement检测结果提交DTO
 */
@Data
public class MovementTestSubmitDTO {

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;


    /**
     * 检测基本信息
     */
    @NotNull(message = "检测信息不能为空")
    private TestInfo testInfo;

    /**
     * 检测结果数据
     */
    @NotNull(message = "检测结果不能为空")
    private TestResultData resultData;

    // 手动添加getter方法以解决IDE识别问题
    public Long getPatientId() {
        return patientId;
    }


    public TestInfo getTestInfo() {
        return testInfo;
    }

    public TestResultData getResultData() {
        return resultData;
    }



    /**
     * 检测信息
     */
    @Data
    public static class TestInfo {
        @NotBlank(message = "检测类型不能为空")
        private String testType; // GAZE_STABILITY, FOLLOW_ABILITY, SACCADE_ABILITY, ROI_DETECTION
        @NotBlank(message = "检测序列号不能为空")
        private String testSequence; // 01, 02, 03, 04
        @NotNull(message = "检测时间不能为空")
        private LocalDateTime testDate;
        private Integer duration;
        private String calibrationParams;
        private String environmentInfo;
        private String notes;
        /**
         * 检测图片URL
         */
        private String imageUrl;
    }

    /**
     * 检测结果数据
     */
    @Data
    public static class TestResultData {
        // 视线轨迹数据
        private List<GazePoint> gazeTrajectory;
        
        // 注视稳定性结果数据
        private GazeStabilityData gazeStabilityData;
        
        // 追随能力结果数据
        private FollowAbilityData followAbilityData;
        
        // 扫视能力结果数据
        private SaccadeAbilityData saccadeAbilityData;
        
        // ROI检测结果数据
        private RoiDetectionData roiDetectionData;
    }

    /**
     * 视线点数据
     */
    @Data
    public static class GazePoint {
        private Integer index;
        private BigDecimal x;
        private BigDecimal y;
        private BigDecimal distance;
        private Integer duration;
        private Long timestamp;
        private Boolean isValid;
        private Boolean skew;
    }

    /**
     * 注视稳定性数据
     */
    @Data
    public static class GazeStabilityData {
        private BigDecimal targetX;
        private BigDecimal targetY;
        private String gazeTrajectoryJson;
        private BigDecimal stabilityScore;
        private String evaluationResult;
        private BigDecimal deviationX;
        private BigDecimal deviationY;
        private Integer fixationDuration;
        private Integer fixationCount;
        private BigDecimal averageDistance;
        private BigDecimal maxDeviation;
        private BigDecimal rmsError;
        private Integer targetPointRadius;
        private Integer gazePointRadius;
        private Integer loopRadiusIncreases;
        private Integer loopsCount;
    }

    /**
     * 追随能力数据
     */
    @Data
    public static class FollowAbilityData {
        private String followPathPoints;
        private String actualGazePoints;
        private BigDecimal trackingAccuracy;
        private BigDecimal averageError;
        private BigDecimal reactionTime;
        private BigDecimal smoothnessScore;
        private BigDecimal completionRate;
        private String bezierCurveData;
        private Integer followCount;
        private Integer successfulFollows;
        private BigDecimal pathLength;
        private BigDecimal actualPathLength;
        private BigDecimal velocityConsistency;
    }

    /**
     * 扫视能力数据
     */
    @Data
    public static class SaccadeAbilityData {
        private String targetPoints;
        private String saccadeEvents;
        private String gazeTrajectoryJson;
        private Integer totalSaccades;
        private Integer successfulSaccades;
        private BigDecimal accuracyRate;
        private BigDecimal averageSaccadeTime;
        private BigDecimal saccadeVelocity;
        private BigDecimal errorDistance;
        private BigDecimal latency;
        private BigDecimal peakVelocity;
        private BigDecimal undershootRate;
        private BigDecimal overshootRate;
        private BigDecimal fixationStability;
    }

    /**
     * ROI检测数据
     */
    @Data
    public static class RoiDetectionData {
        private String imagePath;
        private Integer imageWidth;
        private Integer imageHeight;
        private String roiRegions;
        private String gazeTrajectoryJson;
        private String heatmapData;
        private String attentionDistribution;
        private String scanPath;
        private BigDecimal fixationDurationAvg;
        private Integer fixationCount;
        private Integer saccadeCount;
        private Integer totalViewingTime;
        private BigDecimal roiCoverageRate;
        private String roiDwellTime;
        private BigDecimal firstFixationTime;
        private String scanPatternType;
    }
}
