package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 检测记录主表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("em_test_records")
public class EmTestRecord extends BaseEntity {

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 检测类型
     */
    private String testType;

    /**
     * 检测序列号(01-04)
     */
    private String testSequence;

    /**
     * 检测日期时间
     */
    private LocalDateTime testDate;

    /**
     * 检测时长(秒)
     */
    private Integer duration;

    /**
     * 检测状态
     */
    private String status;

    /**
     * 校准参数
     */
    private String calibrationParams;

    /**
     * 环境信息(光线、距离等)
     */
    private String environmentInfo;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 检测图片URL
     */
    private String imageUrl;
}
