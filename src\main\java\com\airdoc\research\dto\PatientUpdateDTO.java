package com.airdoc.research.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.time.LocalDate;

/**
 * 患者更新DTO
 */
@Data
public class PatientUpdateDTO {

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long id;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 住院号
     */
    private String inpatientNum;

    /**
     * 病例卡号
     */
    private String caseCardNum;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别 1=男 2=女
     */
    private Integer gender;

    /**
     * 患者类型 1=阳性 2=阴性
     */
    private Integer patientType;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 手机号国家代码
     */
    private Integer phoneCountryCode;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 患者个性签名
     */
    private String description;

    /**
     * 诊断信息
     */
    private String diagnosisInformation;

    /**
     * 机构ID
     */
    private String organizationId;

    /**
     * 三方患者编号
     */
    private String thirdBizId;
}
