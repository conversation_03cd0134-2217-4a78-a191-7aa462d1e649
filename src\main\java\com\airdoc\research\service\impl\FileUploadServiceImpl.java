package com.airdoc.research.service.impl;

import com.airdoc.research.dto.FileUploadResponseDTO;
import com.airdoc.research.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务实现类
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {

    // 支持的图片格式
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
    );

    // 最大文件大小（10MB）
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Value("${file.upload.base-url:http://localhost:8080/api}")
    private String baseUrl;

    @Override
    public FileUploadResponseDTO uploadImage(MultipartFile file) {
        log.info("开始上传图片文件，原始文件名：{}", file.getOriginalFilename());

        try {
            // 验证文件
            validateFile(file);

            // 创建上传目录
            createUploadDirectory();

            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());

            // 保存文件
            Path filePath = saveFile(file, fileName);

            // 生成访问URL
            String fileUrl = generateFileUrl(fileName);

            log.info("图片上传成功，文件名：{}，URL：{}", fileName, fileUrl);

            return new FileUploadResponseDTO(
                    fileUrl,
                    file.getOriginalFilename(),
                    fileName,
                    file.getSize(),
                    file.getContentType()
            );

        } catch (Exception e) {
            log.error("图片上传失败", e);
            throw new RuntimeException("图片上传失败：" + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String fileName) {
        try {
            if (!StringUtils.hasText(fileName)) {
                return false;
            }

            Path filePath = Paths.get(uploadPath, fileName);
            boolean deleted = Files.deleteIfExists(filePath);
            
            if (deleted) {
                log.info("文件删除成功：{}", fileName);
            } else {
                log.warn("文件不存在或删除失败：{}", fileName);
            }
            
            return deleted;
        } catch (Exception e) {
            log.error("删除文件失败：{}", fileName, e);
            return false;
        }
    }

    @Override
    public boolean fileExists(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return false;
        }
        
        Path filePath = Paths.get(uploadPath, fileName);
        return Files.exists(filePath);
    }

    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }

        // 检查文件类型
//        String contentType = file.getContentType();
//        if (!ALLOWED_IMAGE_TYPES.contains(contentType)) {
//            throw new IllegalArgumentException("不支持的文件类型，仅支持：" + String.join(", ", ALLOWED_IMAGE_TYPES));
//        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename);
        if (!isValidImageExtension(extension)) {
            throw new IllegalArgumentException("不支持的文件扩展名");
        }
    }

    /**
     * 创建上传目录
     */
    private void createUploadDirectory() throws IOException {
        Path uploadDir = Paths.get(uploadPath);
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
            log.info("创建上传目录：{}", uploadDir.toAbsolutePath());
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%s%s", timestamp, uuid, extension);
    }

    /**
     * 保存文件到磁盘
     */
    private Path saveFile(MultipartFile file, String fileName) throws IOException {
        Path filePath = Paths.get(uploadPath, fileName);
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        return filePath;
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String fileName) {
        return String.format("%s/files/%s", baseUrl, fileName);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * 检查是否为有效的图片扩展名
     */
    private boolean isValidImageExtension(String extension) {
        if (!StringUtils.hasText(extension)) {
            return false;
        }
        String lowerExtension = extension.toLowerCase();
        return lowerExtension.equals(".jpg") || lowerExtension.equals(".jpeg") || 
               lowerExtension.equals(".png") || lowerExtension.equals(".gif") || 
               lowerExtension.equals(".bmp") || lowerExtension.equals(".webp");
    }
}
