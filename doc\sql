-- ========================================
-- Movement模块数据库表结构
-- 包含患者信息表和四个检查结果表
-- 所有表都包含审计字段和软删除功能
-- ========================================

-- ========================================
-- 1. 患者信息表 (基于EMPatient.kt和Patient.kt)
-- ========================================
DROP TABLE IF EXISTS `em_patients`;
CREATE TABLE `em_patients` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '患者ID',
  `patient_uuid` VARCHAR(50) NOT NULL UNIQUE COMMENT '患者UUID',
  `name` VARCHAR(100) NOT NULL COMMENT '患者姓名',
  `inpatient_num` VARCHAR(50) COMMENT '住院号',
  `case_card_num` VARCHAR(50) COMMENT '病例卡号',
  `birthday` DATE COMMENT '出生日期',
  `age` INT COMMENT '年龄',
  `gender` TINYINT COMMENT '性别 1=男 2=女',
  `patient_type` TINYINT COMMENT '患者类型 1=阳性 2=阴性',
  `phone` VARCHAR(20) COMMENT '手机号',
  `phone_country_code` INT DEFAULT 86 COMMENT '手机号国家代码',
  `email` VARCHAR(100) COMMENT '邮箱',
  `avatar` VARCHAR(500) COMMENT '头像URL',
  `description` TEXT COMMENT '患者个性签名',
  `diagnosis_information` TEXT COMMENT '诊断信息',
  `organization_id` VARCHAR(50) COMMENT '机构ID',
  `third_biz_id` VARCHAR(50) COMMENT '三方患者编号',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_patient_uuid` (`patient_uuid`),
  KEY `idx_name` (`name`),
  KEY `idx_inpatient_num` (`inpatient_num`),
  KEY `idx_case_card_num` (`case_card_num`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者信息表';

-- ========================================
-- 2. 系统用户表 (操作员信息)
-- ========================================
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  `account_name` VARCHAR(100) COMMENT '账号名称',
  `email` VARCHAR(100) COMMENT '邮箱',
  `phone` VARCHAR(20) COMMENT '手机号',
  `gender` ENUM('MALE','FEMALE','UNKNOWN') DEFAULT 'UNKNOWN' COMMENT '性别',
  `age` INT COMMENT '年龄',
  `hospital_name` VARCHAR(200) COMMENT '医院名称',
  `role` ENUM('DOCTOR','NURSE','TECHNICIAN','ADMIN') DEFAULT 'TECHNICIAN' COMMENT '角色',
  `status` ENUM('ACTIVE','INACTIVE','LOCKED') DEFAULT 'ACTIVE' COMMENT '状态',
  `expiration_date` DATE COMMENT '账号过期日期',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- ========================================
-- 3. 设备信息表
-- ========================================
DROP TABLE IF EXISTS `sys_devices`;
CREATE TABLE `sys_devices` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `device_sn` VARCHAR(50) NOT NULL UNIQUE COMMENT '设备序列号',
  `device_name` VARCHAR(100) COMMENT '设备名称',
  `brand` INT COMMENT '品牌',
  `placement_type` ENUM('M_HOME','M_HOSPITAL','R_HOME','R_STORE','TSC') COMMENT '设备环境类型',
  `firmware_version` VARCHAR(20) COMMENT '固件版本',
  `os_version` VARCHAR(20) COMMENT '操作系统版本',
  `is_demo_mode` BOOLEAN DEFAULT FALSE COMMENT '是否演示模式',
  `is_overseas` BOOLEAN DEFAULT FALSE COMMENT '是否海外设备',
  `is_debug_enable` BOOLEAN DEFAULT FALSE COMMENT '是否开启调试',
  `placement_type_switch` BOOLEAN DEFAULT FALSE COMMENT '支持SKU切换',
  `iot_config` JSON COMMENT 'IoT配置信息',
  `logo` VARCHAR(500) COMMENT '首页logo地址',
  `last_sync_time` TIMESTAMP COMMENT '最后同步时间',
  `device_status` ENUM('ONLINE','OFFLINE','MAINTENANCE') DEFAULT 'OFFLINE' COMMENT '设备状态',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_sn` (`device_sn`),
  KEY `idx_device_status` (`device_status`),
  KEY `idx_placement_type` (`placement_type`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- ========================================
-- 4. 检测记录主表
-- ========================================
DROP TABLE IF EXISTS `em_test_records`;
CREATE TABLE `em_test_records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `patient_id` BIGINT NOT NULL COMMENT '患者ID',
  `device_id` BIGINT NOT NULL COMMENT '设备ID',
  `operator_id` BIGINT NOT NULL COMMENT '操作员ID',
  `test_type` ENUM('GAZE_STABILITY','FOLLOW_ABILITY','SACCADE_ABILITY','ROI_DETECTION') NOT NULL COMMENT '检测类型',
  `test_sequence` VARCHAR(10) NOT NULL COMMENT '检测序列号(01-04)',
  `test_date` DATETIME NOT NULL COMMENT '检测日期时间',
  `duration` INT COMMENT '检测时长(秒)',
  `status` ENUM('COMPLETED','FAILED','INTERRUPTED') DEFAULT 'COMPLETED' COMMENT '检测状态',
  `calibration_params` JSON COMMENT '校准参数',
  `environment_info` JSON COMMENT '环境信息(光线、距离等)',
  `notes` TEXT COMMENT '备注信息',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  KEY `idx_patient_test_date` (`patient_id`, `test_date`),
  KEY `idx_device_test_type` (`device_id`, `test_type`),
  KEY `idx_test_sequence` (`test_sequence`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`patient_id`) REFERENCES `em_patients`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`device_id`) REFERENCES `sys_devices`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`operator_id`) REFERENCES `sys_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检测记录表';

-- ========================================
-- 5. 视线轨迹数据表 (存储所有检测的轨迹点数据)
-- ========================================
DROP TABLE IF EXISTS `gaze_trajectory_data`;
CREATE TABLE `gaze_trajectory_data` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '轨迹点ID',
  `record_id` BIGINT NOT NULL COMMENT '检测记录ID',
  `point_index` INT NOT NULL COMMENT '点序号',
  `x_coordinate` DECIMAL(8,6) NOT NULL COMMENT 'x坐标(0-1比例)',
  `y_coordinate` DECIMAL(8,6) NOT NULL COMMENT 'y坐标(0-1比例)',
  `distance` DECIMAL(5,2) COMMENT '距离(cm)',
  `duration` INT COMMENT '持续时间(ms)',
  `timestamp` BIGINT COMMENT '时间戳',
  `is_valid` BOOLEAN DEFAULT TRUE COMMENT '数据有效性',
  `skew` BOOLEAN DEFAULT FALSE COMMENT '姿势是否偏移',
  `raw_data` JSON COMMENT '原始检测数据',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  KEY `idx_record_point` (`record_id`, `point_index`),
  KEY `idx_coordinates` (`x_coordinate`, `y_coordinate`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`record_id`) REFERENCES `em_test_records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视线轨迹数据表';

-- ========================================
-- 6. 注视稳定性检测结果表 (序列号01)
-- ========================================
DROP TABLE IF EXISTS `gaze_stability_results`;
CREATE TABLE `gaze_stability_results` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `record_id` BIGINT NOT NULL COMMENT '检测记录ID',
  `target_x` DECIMAL(8,6) NOT NULL COMMENT '目标点x坐标(0-1)',
  `target_y` DECIMAL(8,6) NOT NULL COMMENT '目标点y坐标(0-1)',
  `gaze_trajectory_json` JSON NOT NULL COMMENT '视线轨迹JSON数据',
  `stability_score` DECIMAL(5,2) COMMENT '稳定性评分',
  `evaluation_result` ENUM('STABLE','UNSTABLE','MODERATE') COMMENT '评估结果',
  `deviation_x` DECIMAL(8,6) COMMENT 'x轴平均偏移',
  `deviation_y` DECIMAL(8,6) COMMENT 'y轴平均偏移',
  `fixation_duration` INT COMMENT '注视总时长(ms)',
  `fixation_count` INT COMMENT '有效注视点数量',
  `average_distance` DECIMAL(5,2) COMMENT '平均距离(cm)',
  `max_deviation` DECIMAL(8,6) COMMENT '最大偏移距离',
  `rms_error` DECIMAL(8,6) COMMENT '均方根误差',
  `target_point_radius` INT COMMENT '目标点半径(px)',
  `gaze_point_radius` INT COMMENT '视点半径(px)',
  `loop_radius_increases` INT COMMENT '环半径增长值(px)',
  `loops_count` INT COMMENT '环数',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_stability_score` (`stability_score`),
  KEY `idx_evaluation_result` (`evaluation_result`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`record_id`) REFERENCES `em_test_records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='注视稳定性检测结果表';

-- ========================================
-- 7. 追随能力检测结果表 (序列号02)
-- ========================================
DROP TABLE IF EXISTS `follow_ability_results`;
CREATE TABLE `follow_ability_results` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `record_id` BIGINT NOT NULL COMMENT '检测记录ID',
  `follow_path_points` JSON NOT NULL COMMENT '目标路径点(贝塞尔曲线)',
  `actual_gaze_points` JSON NOT NULL COMMENT '实际视线点',
  `tracking_accuracy` DECIMAL(5,2) COMMENT '跟踪准确率(%)',
  `average_error` DECIMAL(8,4) COMMENT '平均误差距离',
  `reaction_time` DECIMAL(8,2) COMMENT '反应时间(ms)',
  `smoothness_score` DECIMAL(5,2) COMMENT '平滑度评分',
  `completion_rate` DECIMAL(5,2) COMMENT '完成率(%)',
  `bezier_curve_data` JSON COMMENT '贝塞尔曲线控制点数据',
  `follow_count` INT COMMENT '追随次数',
  `successful_follows` INT COMMENT '成功追随次数',
  `path_length` DECIMAL(8,2) COMMENT '路径总长度(px)',
  `actual_path_length` DECIMAL(8,2) COMMENT '实际追随路径长度(px)',
  `velocity_consistency` DECIMAL(5,2) COMMENT '速度一致性评分',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_tracking_accuracy` (`tracking_accuracy`),
  KEY `idx_completion_rate` (`completion_rate`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`record_id`) REFERENCES `em_test_records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='追随能力检测结果表';

-- ========================================
-- 8. 扫视能力检测结果表 (序列号03)
-- ========================================
DROP TABLE IF EXISTS `saccade_ability_results`;
CREATE TABLE `saccade_ability_results` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `record_id` BIGINT NOT NULL COMMENT '检测记录ID',
  `target_points` JSON NOT NULL COMMENT '目标点序列',
  `saccade_events` JSON NOT NULL COMMENT '扫视事件详情',
  `gaze_trajectory_json` JSON NOT NULL COMMENT '视线轨迹JSON数据',
  `total_saccades` INT COMMENT '总扫视次数',
  `successful_saccades` INT COMMENT '成功扫视次数',
  `accuracy_rate` DECIMAL(5,2) COMMENT '准确率(%)',
  `average_saccade_time` DECIMAL(8,2) COMMENT '平均扫视时间(ms)',
  `saccade_velocity` DECIMAL(8,2) COMMENT '平均扫视速度(度/秒)',
  `error_distance` DECIMAL(8,4) COMMENT '平均误差距离',
  `latency` DECIMAL(8,2) COMMENT '扫视潜伏期(ms)',
  `peak_velocity` DECIMAL(8,2) COMMENT '峰值速度(度/秒)',
  `undershoot_rate` DECIMAL(5,2) COMMENT '欠冲率(%)',
  `overshoot_rate` DECIMAL(5,2) COMMENT '过冲率(%)',
  `fixation_stability` DECIMAL(5,2) COMMENT '注视稳定性评分',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_accuracy_rate` (`accuracy_rate`),
  KEY `idx_saccade_velocity` (`saccade_velocity`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`record_id`) REFERENCES `em_test_records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫视能力检测结果表';

-- ========================================
-- 9. ROI检测结果表 (序列号04)
-- ========================================
DROP TABLE IF EXISTS `roi_detection_results`;
CREATE TABLE `roi_detection_results` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `record_id` BIGINT NOT NULL COMMENT '检测记录ID',
  `image_path` VARCHAR(500) NOT NULL COMMENT '测试图像路径',
  `image_width` INT COMMENT '图像宽度(px)',
  `image_height` INT COMMENT '图像高度(px)',
  `roi_regions` JSON COMMENT '兴趣区域定义',
  `gaze_trajectory_json` JSON NOT NULL COMMENT '视线轨迹JSON数据',
  `heatmap_data` JSON COMMENT '热力图数据',
  `attention_distribution` JSON COMMENT '注意力分布统计',
  `scan_path` JSON COMMENT '扫描路径数据',
  `fixation_duration_avg` DECIMAL(8,2) COMMENT '平均注视时长(ms)',
  `fixation_count` INT COMMENT '注视点总数',
  `saccade_count` INT COMMENT '扫视次数',
  `total_viewing_time` INT COMMENT '总观看时间(ms)',
  `roi_coverage_rate` DECIMAL(5,2) COMMENT 'ROI覆盖率(%)',
  `roi_dwell_time` JSON COMMENT '各ROI停留时间统计',
  `first_fixation_time` DECIMAL(8,2) COMMENT '首次注视时间(ms)',
  `scan_pattern_type` VARCHAR(50) COMMENT '扫描模式类型',
  -- 审计字段
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` BIGINT COMMENT '创建人ID',
  `updated_by` BIGINT COMMENT '修改人ID',
  `is_deleted` BOOLEAN DEFAULT FALSE COMMENT '逻辑删除标记',
  `deleted_at` TIMESTAMP NULL COMMENT '删除时间',
  `deleted_by` BIGINT NULL COMMENT '删除人ID',
  `version` INT DEFAULT 1 COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_roi_coverage_rate` (`roi_coverage_rate`),
  KEY `idx_scan_pattern_type` (`scan_pattern_type`),
  KEY `idx_is_deleted` (`is_deleted`),
  FOREIGN KEY (`record_id`) REFERENCES `em_test_records`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ROI检测结果表';
