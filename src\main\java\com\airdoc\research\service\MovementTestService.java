package com.airdoc.research.service;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.dto.*;

import java.util.List;

/**
 * Movement检测服务接口
 */
public interface MovementTestService {

    /**
     * 提交检测结果
     * @param submitDTO 检测结果数据
     * @param deviceSn 设备序列号
     * @return 检测记录ID
     */
    Long submitTestResult(MovementTestSubmitDTO submitDTO, String deviceSn);

    /**
     * 分页查询注视稳定性测试列表
     * @param queryDTO 分页查询参数
     * @return 分页响应结果
     */
    PageResponse<GazeStabilityResponseDTO> pageQueryGazeStabilityTests(GazeStabilityPageQueryDTO queryDTO);

    /**
     * 查询注视稳定性测试列表
     * @param queryDTO 查询参数
     * @return 测试列表
     */
    List<GazeStabilityResponseDTO> queryGazeStabilityTests(GazeStabilityQueryDTO queryDTO);

    /**
     * 根据ID查询注视稳定性测试详情
     * @param id 结果ID
     * @return 测试详情
     */
    GazeStabilityResponseDTO getGazeStabilityTestById(Long id);

    /**
     * 根据记录ID查询注视稳定性测试详情
     * @param recordId 检测记录ID
     * @return 测试详情
     */
    GazeStabilityResponseDTO getGazeStabilityTestByRecordId(Long recordId);

    /**
     * 统计注视稳定性测试数量
     * @param queryDTO 查询参数
     * @return 测试数量
     */
    Long countGazeStabilityTests(GazeStabilityQueryDTO queryDTO);

    // ========== 扫视能力检测查询方法 ==========

    /**
     * 分页查询扫视能力检测列表
     * @param queryDTO 分页查询参数
     * @return 分页响应结果
     */
    PageResponse<SaccadeAbilityResponseDTO> pageQuerySaccadeAbilityTests(SaccadeAbilityPageQueryDTO queryDTO);

    /**
     * 查询扫视能力检测列表
     * @param queryDTO 查询参数
     * @return 测试列表
     */
    List<SaccadeAbilityResponseDTO> querySaccadeAbilityTests(SaccadeAbilityQueryDTO queryDTO);

    /**
     * 根据ID查询扫视能力检测详情
     * @param id 结果ID
     * @return 测试详情
     */
    SaccadeAbilityResponseDTO getSaccadeAbilityTestById(Long id);

    /**
     * 根据记录ID查询扫视能力检测详情
     * @param recordId 检测记录ID
     * @return 测试详情
     */
    SaccadeAbilityResponseDTO getSaccadeAbilityTestByRecordId(Long recordId);

    /**
     * 统计扫视能力检测数量
     * @param queryDTO 查询参数
     * @return 测试数量
     */
    Long countSaccadeAbilityTests(SaccadeAbilityQueryDTO queryDTO);
}
