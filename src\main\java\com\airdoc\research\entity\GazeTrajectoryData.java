package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 视线轨迹数据实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gaze_trajectory_data")
public class GazeTrajectoryData extends BaseEntity {

    /**
     * 轨迹点ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 点序号
     */
    private Integer pointIndex;

    /**
     * x坐标(0-1比例)
     */
    private BigDecimal xCoordinate;

    /**
     * y坐标(0-1比例)
     */
    private BigDecimal yCoordinate;

    /**
     * 距离(cm)
     */
    private BigDecimal distance;

    /**
     * 持续时间(ms)
     */
    private Integer duration;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 数据有效性
     */
    private Boolean isValid;

    /**
     * 姿势是否偏移
     */
    private Boolean skew;

    /**
     * 原始检测数据
     */
    private String rawData;
}
