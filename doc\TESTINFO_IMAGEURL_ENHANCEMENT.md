# TestInfo接口增加imageUrl字段功能说明

## 概述

根据需求，为提交testinfo的接口增加了一个`imageUrl`字段来接收图片URL，使得在提交检测结果时可以直接关联图片。

## 修改内容

### 1. DTO类修改

**文件：** `src/main/java/com/airdoc/research/dto/MovementTestSubmitDTO.java`

在 `TestInfo` 内部类中添加了 `imageUrl` 字段：

```java
/**
 * 检测信息
 */
@Data
public static class TestInfo {
    @NotBlank(message = "检测类型不能为空")
    private String testType;
    @NotBlank(message = "检测序列号不能为空")
    private String testSequence;
    @NotNull(message = "检测时间不能为空")
    private LocalDateTime testDate;
    private Integer duration;
    private String calibrationParams;
    private String environmentInfo;
    private String notes;
    /**
     * 检测图片URL
     */
    private String imageUrl;  // 新增字段
}
```

### 2. 服务层修改

**文件：** `src/main/java/com/airdoc/research/service/impl/MovementTestServiceImpl.java`

修改了 `createTestRecord` 方法，增加了对 `imageUrl` 字段的处理：

```java
private Long createTestRecord(MovementTestSubmitDTO.TestInfo testInfo, Long patientId, Long operatorId, Long deviceId) {
    EmTestRecord testRecord = new EmTestRecord();
    BeanUtils.copyProperties(testInfo, testRecord);
    
    testRecord.setPatientId(patientId);
    testRecord.setOperatorId(operatorId);
    testRecord.setDeviceId(deviceId);
    testRecord.setStatus("COMPLETED");
    
    // 设置图片URL（如果提供了的话）
    if (testInfo.getImageUrl() != null && !testInfo.getImageUrl().trim().isEmpty()) {
        testRecord.setImageUrl(testInfo.getImageUrl());
        log.info("设置检测记录图片URL：{}", testInfo.getImageUrl());
    }
    
    testRecordMapper.insert(testRecord);
    return testRecord.getId();
}
```

**文件：** `src/main/java/com/airdoc/research/service/impl/MovementTestServiceResourceImpl.java`

同样修改了 `createTestRecord` 方法，保持两个实现类的一致性。

### 3. 实体类修改

**文件：** `src/main/java/com/airdoc/research/entity/EmTestRecord.java`

添加了 `imageUrl` 字段：

```java
/**
 * 检测图片URL
 */
private String imageUrl;
```

### 4. 数据库表结构修改

**文件：** `doc/sql/add_image_url_to_test_records.sql`

为 `em_test_records` 表添加 `image_url` 字段：

```sql
ALTER TABLE `em_test_records` 
ADD COLUMN `image_url` VARCHAR(500) NULL COMMENT '检测图片URL' AFTER `notes`;
```

### 5. 测试代码

**文件：** `src/test/java/com/airdoc/research/controller/MovementTestControllerImageUrlTest.java`

创建了专门的测试类来验证 `imageUrl` 字段的功能。

### 6. 文档更新

- **文件：** `doc/FILE_UPLOAD_API.md` - 更新了API文档，说明如何使用imageUrl字段
- **文件：** `doc/MOVEMENT_TEST_WITH_IMAGE_EXAMPLE.md` - 创建了完整的使用示例
- **文件：** `src/main/resources/static/upload-test.html` - 更新了测试页面，支持测试新功能

## 使用方式

### 方式一：先上传图片，再提交检测结果

1. **上传图片获取URL：**
```bash
curl -X POST http://localhost:8080/api/files/upload/image \
  -F 'file=@image.jpg'
```

2. **提交检测结果时包含imageUrl：**
```bash
curl -X POST http://localhost:8080/api/movement/submit \
  -H 'Content-Type: application/json' \
  -H 'X-Device-Sn: DEVICE001' \
  -d '{
    "patientId": 123,
    "testInfo": {
      "testType": "GAZE_STABILITY",
      "testSequence": "01",
      "testDate": "2024-12-20T14:30:22",
      "imageUrl": "http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg"
    },
    "resultData": { ... }
  }'
```

### 方式二：后续关联图片

1. **先提交检测结果**
2. **使用返回的记录ID上传图片：**
```bash
curl -X POST http://localhost:8080/api/files/upload/image/test-record/456 \
  -F 'file=@image.jpg'
```

## 接口兼容性

- ✅ **向后兼容**：`imageUrl` 字段是可选的，现有的接口调用不会受到影响
- ✅ **数据库兼容**：新增的 `image_url` 字段允许为NULL，不影响现有数据
- ✅ **服务兼容**：两个服务实现类都已更新，保持功能一致性

## 支持的检测类型

所有检测类型都支持 `imageUrl` 字段：

- `GAZE_STABILITY` (注视稳定性)
- `FOLLOW_ABILITY` (追随能力)  
- `SACCADE_ABILITY` (扫视能力)
- `ROI_DETECTION` (ROI检测)

## 验证方法

1. **启动应用**
2. **执行数据库脚本**：`doc/sql/add_image_url_to_test_records.sql`
3. **访问测试页面**：`http://localhost:8080/api/upload-test.html`
4. **使用第3个测试区域**测试新功能

## 注意事项

1. **imageUrl字段是可选的**，可以为null或空字符串
2. **URL格式验证**：建议使用完整的URL格式
3. **图片持久性**：确保提供的URL是持久可访问的
4. **数据一致性**：推荐使用方式一，确保图片和检测记录的一致性
5. **错误处理**：如果imageUrl无效，不会影响检测结果的正常提交

## 总结

通过这次增强，现在可以在提交检测结果时直接包含图片URL，实现了检测记录与图片的无缝关联。这个功能既保持了向后兼容性，又提供了更灵活的图片关联方式。
