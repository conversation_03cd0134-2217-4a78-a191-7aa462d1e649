package com.airdoc.research.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 依赖注入示例配置类
 * 展示Spring Boot 3推荐的依赖注入方式
 */
@Configuration
public class DependencyInjectionExampleConfig {

    // 方式1: 使用@Resource注解（JSR-250标准，Spring Boot 3推荐）
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 示例Bean配置
     * 展示如何在配置类中使用@Resource注入依赖
     */
    @Bean
    public String dependencyInjectionExample() {
        return "使用@Resource注解进行依赖注入 - Spring Boot 3推荐方式";
    }

    // 注意：在配置类中，构造器注入仍然是最佳实践
    // 但对于简单的字段注入，@Resource是一个很好的选择
}
