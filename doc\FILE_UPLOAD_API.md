# 文件上传API文档

## 概述

本系统提供了图片上传功能，支持将图片上传到服务器并获取访问URL。特别地，提供了专门的接口用于上传图片并自动更新EmTestRecord（检测记录）的imageUrl字段。

## 支持的文件格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- WebP (.webp)

## 文件大小限制

- 最大文件大小：10MB

## API接口

### 1. 通用图片上传

**接口地址：** `POST /api/files/upload/image`

**请求参数：**
- `file`: MultipartFile - 上传的图片文件

**请求示例：**
```bash
curl -X POST \
  http://localhost:8080/api/files/upload/image \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/your/image.jpg'
```

**响应示例：**
```json
{
  "code": 200,
  "message": "图片上传成功",
  "data": {
    "url": "http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg",
    "originalFileName": "image.jpg",
    "fileName": "20241220_143022_a1b2c3d4.jpg",
    "fileSize": 102400,
    "contentType": "image/jpeg"
  },
  "timestamp": 1703058622000
}
```

### 2. 检测记录图片上传

**接口地址：** `POST /api/files/upload/image/test-record/{recordId}`

**路径参数：**
- `recordId`: Long - 检测记录ID

**请求参数：**
- `file`: MultipartFile - 上传的图片文件

**功能说明：**
- 上传图片文件到服务器
- 自动更新指定检测记录的imageUrl字段
- 如果更新失败，会自动删除已上传的文件

**请求示例：**
```bash
curl -X POST \
  http://localhost:8080/api/files/upload/image/test-record/123 \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/your/image.jpg'
```

**响应示例：**
```json
{
  "code": 200,
  "message": "检测记录图片上传成功",
  "data": {
    "url": "http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg",
    "originalFileName": "image.jpg",
    "fileName": "20241220_143022_a1b2c3d4.jpg",
    "fileSize": 102400,
    "contentType": "image/jpeg"
  },
  "timestamp": 1703058622000
}
```

### 3. 文件访问

**接口地址：** `GET /api/files/{fileName}`

**路径参数：**
- `fileName`: String - 文件名

**功能说明：**
- 直接访问上传的文件
- 支持在浏览器中直接显示图片

**请求示例：**
```bash
curl -X GET http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg
```

### 4. 删除文件

**接口地址：** `DELETE /api/files/{fileName}`

**路径参数：**
- `fileName`: String - 文件名

**请求示例：**
```bash
curl -X DELETE http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg
```

**响应示例：**
```json
{
  "code": 200,
  "message": "文件删除成功",
  "data": true,
  "timestamp": 1703058622000
}
```

### 5. 检查文件是否存在

**接口地址：** `GET /api/files/{fileName}/exists`

**路径参数：**
- `fileName`: String - 文件名

**请求示例：**
```bash
curl -X GET http://localhost:8080/api/files/20241220_143022_a1b2c3d4.jpg/exists
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true,
  "timestamp": 1703058622000
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
  - 文件为空
  - 文件类型不支持
  - 文件大小超限
  - 检测记录不存在

- `500`: 服务器内部错误
  - 文件保存失败
  - 数据库更新失败

### 错误响应示例

```json
{
  "code": 400,
  "message": "不支持的文件类型，仅支持：image/jpeg, image/jpg, image/png, image/gif, image/bmp, image/webp",
  "data": null,
  "timestamp": 1703058622000
}
```

## 配置说明

在 `application.properties` 中可以配置以下参数：

```properties
# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.file-size-threshold=2KB

# 自定义文件上传配置
file.upload.path=uploads
file.upload.base-url=http://localhost:8080/api
```

## 数据库变更

需要执行以下SQL语句为检测记录表添加图片URL字段：

```sql
ALTER TABLE `em_test_records` 
ADD COLUMN `image_url` VARCHAR(500) NULL COMMENT '检测图片URL' AFTER `notes`;
```

## 使用建议

1. **推荐使用检测记录图片上传接口**：如果需要将图片关联到特定的检测记录，建议使用 `/api/files/upload/image/test-record/{recordId}` 接口，它会自动处理数据库更新。

2. **文件命名规则**：系统会自动生成唯一的文件名，格式为：`yyyyMMdd_HHmmss_随机字符串.扩展名`

3. **文件存储位置**：文件默认存储在项目根目录的 `uploads` 文件夹中。

4. **安全考虑**：
   - 系统会验证文件类型和大小
   - 只允许上传指定格式的图片文件
   - 文件名会被重新生成以避免冲突和安全问题
