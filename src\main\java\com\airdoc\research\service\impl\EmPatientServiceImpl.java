package com.airdoc.research.service.impl;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.dto.*;
import com.airdoc.research.entity.EmPatient;
import com.airdoc.research.mapper.EmPatientMapper;
import com.airdoc.research.service.EmPatientService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 患者管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmPatientServiceImpl implements EmPatientService {

    private final EmPatientMapper patientMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PatientIdResponseDTO createPatient(PatientCreateDTO createDTO) {
        log.info("创建患者，姓名：{}", createDTO.getName());

        // 创建患者实体
        EmPatient patient = new EmPatient();
        BeanUtils.copyProperties(createDTO, patient);

        // 生成UUID
        patient.setPatientUuid(UUID.randomUUID().toString());

        // 保存患者
        patientMapper.insert(patient);

        log.info("患者创建成功，ID：{}，UUID：{}", patient.getId(), patient.getPatientUuid());

        return new PatientIdResponseDTO(patient.getId(), patient.getPatientUuid());
    }

    @Override
    public PatientResponseDTO getPatientById(Long id) {
        log.debug("根据ID查询患者：{}", id);
        
        EmPatient patient = patientMapper.selectById(id);
        if (patient == null) {
            throw new RuntimeException("患者不存在，ID：" + id);
        }
        
        return convertToResponseDTO(patient);
    }

    @Override
    public PatientResponseDTO getPatientByUuid(String patientUuid) {
        log.debug("根据UUID查询患者：{}", patientUuid);
        
        EmPatient patient = patientMapper.selectOne(
            Wrappers.<EmPatient>lambdaQuery()
                .eq(EmPatient::getPatientUuid, patientUuid)
        );
        
        if (patient == null) {
            throw new RuntimeException("患者不存在，UUID：" + patientUuid);
        }
        
        return convertToResponseDTO(patient);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PatientResponseDTO updatePatient(PatientUpdateDTO updateDTO) {
        log.info("更新患者信息，ID：{}", updateDTO.getId());
        
        // 检查患者是否存在
        EmPatient existingPatient = patientMapper.selectById(updateDTO.getId());
        if (existingPatient == null) {
            throw new RuntimeException("患者不存在，ID：" + updateDTO.getId());
        }
        
        // 更新患者信息
        BeanUtils.copyProperties(updateDTO, existingPatient, "id", "patientUuid", "createdAt", "createdBy");
        
        patientMapper.updateById(existingPatient);
        
        log.info("患者信息更新成功，ID：{}", updateDTO.getId());
        
        return convertToResponseDTO(existingPatient);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePatient(Long id) {
        log.info("删除患者，ID：{}", id);
        
        // 检查患者是否存在
        EmPatient patient = patientMapper.selectById(id);
        if (patient == null) {
            throw new RuntimeException("患者不存在，ID：" + id);
        }
        
        // 逻辑删除
        int result = patientMapper.deleteById(id);
        
        log.info("患者删除成功，ID：{}", id);
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeletePatients(List<Long> ids) {
        log.info("批量删除患者，数量：{}", ids.size());
        
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        int result = patientMapper.deleteBatchIds(ids);
        
        log.info("批量删除患者完成，删除数量：{}", result);
        
        return result;
    }

    @Override
    public PageResponse<PatientResponseDTO> pageQueryPatients(PatientPageQueryDTO queryDTO) {
        log.debug("分页查询患者，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());
        
        // 验证分页参数
        queryDTO.validate();
        
        // 构建查询条件
        LambdaQueryWrapper<EmPatient> queryWrapper = buildQueryWrapper(queryDTO);
        
        // 查询总数
        Long total = patientMapper.selectCount(queryWrapper);
        
        if (total == 0) {
            return PageResponse.empty(queryDTO.getCurrent(), queryDTO.getSize());
        }
        
        // 添加排序
        addOrderBy(queryWrapper, queryDTO);
        
        // 分页查询
        List<EmPatient> patients = patientMapper.selectList(queryWrapper
            .last("LIMIT " + queryDTO.getOffset() + ", " + queryDTO.getSize()));
        
        // 转换为响应DTO
        List<PatientResponseDTO> responseDTOs = patients.stream()
            .map(this::convertToResponseDTO)
            .collect(Collectors.toList());
        
        return PageResponse.of(queryDTO.getCurrent(), queryDTO.getSize(), total, responseDTOs);
    }

    @Override
    public List<PatientResponseDTO> queryPatients(PatientQueryDTO queryDTO) {
        log.debug("查询患者列表");
        
        // 构建查询条件
        LambdaQueryWrapper<EmPatient> queryWrapper = buildQueryWrapper(queryDTO);
        
        // 查询患者列表
        List<EmPatient> patients = patientMapper.selectList(queryWrapper);
        
        // 转换为响应DTO
        return patients.stream()
            .map(this::convertToResponseDTO)
            .collect(Collectors.toList());
    }

    @Override
    public Boolean existsById(Long id) {
        return patientMapper.selectById(id) != null;
    }

    @Override
    public Boolean existsByUuid(String patientUuid) {
        return patientMapper.selectCount(
            Wrappers.<EmPatient>lambdaQuery()
                .eq(EmPatient::getPatientUuid, patientUuid)
        ) > 0;
    }

    @Override
    public PatientResponseDTO getPatientByInpatientNum(String inpatientNum) {
        log.debug("根据住院号查询患者：{}", inpatientNum);
        
        EmPatient patient = patientMapper.selectOne(
            Wrappers.<EmPatient>lambdaQuery()
                .eq(EmPatient::getInpatientNum, inpatientNum)
        );
        
        if (patient == null) {
            throw new RuntimeException("患者不存在，住院号：" + inpatientNum);
        }
        
        return convertToResponseDTO(patient);
    }

    @Override
    public PatientResponseDTO getPatientByCaseCardNum(String caseCardNum) {
        log.debug("根据病例卡号查询患者：{}", caseCardNum);
        
        EmPatient patient = patientMapper.selectOne(
            Wrappers.<EmPatient>lambdaQuery()
                .eq(EmPatient::getCaseCardNum, caseCardNum)
        );
        
        if (patient == null) {
            throw new RuntimeException("患者不存在，病例卡号：" + caseCardNum);
        }
        
        return convertToResponseDTO(patient);
    }

    @Override
    public Long countPatients() {
        return patientMapper.selectCount(null);
    }

    @Override
    public Long countPatientsByOrganization(String organizationId) {
        return patientMapper.selectCount(
            Wrappers.<EmPatient>lambdaQuery()
                .eq(EmPatient::getOrganizationId, organizationId)
        );
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<EmPatient> buildQueryWrapper(Object queryDTO) {
        LambdaQueryWrapper<EmPatient> queryWrapper = Wrappers.lambdaQuery();
        
        if (queryDTO instanceof PatientQueryDTO) {
            PatientQueryDTO dto = (PatientQueryDTO) queryDTO;
            buildCommonQueryConditions(queryWrapper, dto);
        } else if (queryDTO instanceof PatientPageQueryDTO) {
            PatientPageQueryDTO dto = (PatientPageQueryDTO) queryDTO;
            buildCommonQueryConditions(queryWrapper, dto);
        }
        
        return queryWrapper;
    }

    /**
     * 构建通用查询条件
     */
    private void buildCommonQueryConditions(LambdaQueryWrapper<EmPatient> queryWrapper, Object queryDTO) {
        // 使用反射或者直接转换来处理通用字段
        // 这里简化处理，实际项目中可以使用更优雅的方式
        if (queryDTO instanceof PatientQueryDTO) {
            PatientQueryDTO dto = (PatientQueryDTO) queryDTO;
            addQueryConditions(queryWrapper, dto);
        } else if (queryDTO instanceof PatientPageQueryDTO) {
            PatientPageQueryDTO dto = (PatientPageQueryDTO) queryDTO;
            addQueryConditions(queryWrapper, dto);
        }
    }

    /**
     * 添加查询条件
     */
    private void addQueryConditions(LambdaQueryWrapper<EmPatient> queryWrapper, Object dto) {
        // 这里需要根据具体的DTO类型来添加条件
        // 为了简化，我们使用instanceof来判断
        String name = null;
        String inpatientNum = null;
        String caseCardNum = null;
        String patientUuid = null;
        Integer gender = null;
        Integer patientType = null;
        String phone = null;
        String email = null;
        String organizationId = null;
        String thirdBizId = null;
        
        if (dto instanceof PatientQueryDTO) {
            PatientQueryDTO queryDto = (PatientQueryDTO) dto;
            name = queryDto.getName();
            inpatientNum = queryDto.getInpatientNum();
            caseCardNum = queryDto.getCaseCardNum();
            patientUuid = queryDto.getPatientUuid();
            gender = queryDto.getGender();
            patientType = queryDto.getPatientType();
            phone = queryDto.getPhone();
            email = queryDto.getEmail();
            organizationId = queryDto.getOrganizationId();
            thirdBizId = queryDto.getThirdBizId();
        } else if (dto instanceof PatientPageQueryDTO) {
            PatientPageQueryDTO pageDto = (PatientPageQueryDTO) dto;
            name = pageDto.getName();
            inpatientNum = pageDto.getInpatientNum();
            caseCardNum = pageDto.getCaseCardNum();
            patientUuid = pageDto.getPatientUuid();
            gender = pageDto.getGender();
            patientType = pageDto.getPatientType();
            phone = pageDto.getPhone();
            email = pageDto.getEmail();
            organizationId = pageDto.getOrganizationId();
            thirdBizId = pageDto.getThirdBizId();
        }
        
        queryWrapper
            .like(StringUtils.hasText(name), EmPatient::getName, name)
            .eq(StringUtils.hasText(inpatientNum), EmPatient::getInpatientNum, inpatientNum)
            .eq(StringUtils.hasText(caseCardNum), EmPatient::getCaseCardNum, caseCardNum)
            .eq(StringUtils.hasText(patientUuid), EmPatient::getPatientUuid, patientUuid)
            .eq(gender != null, EmPatient::getGender, gender)
            .eq(patientType != null, EmPatient::getPatientType, patientType)
            .eq(StringUtils.hasText(phone), EmPatient::getPhone, phone)
            .eq(StringUtils.hasText(email), EmPatient::getEmail, email)
            .eq(StringUtils.hasText(organizationId), EmPatient::getOrganizationId, organizationId)
            .eq(StringUtils.hasText(thirdBizId), EmPatient::getThirdBizId, thirdBizId);
    }

    /**
     * 添加排序条件
     */
    private void addOrderBy(LambdaQueryWrapper<EmPatient> queryWrapper, PatientPageQueryDTO queryDTO) {
        String sortField = queryDTO.getSortField();
        String sortOrder = queryDTO.getSortOrder();
        
        if (StringUtils.hasText(sortField)) {
            boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);
            
            switch (sortField) {
                case "id":
                    queryWrapper.orderBy(true, isAsc, EmPatient::getId);
                    break;
                case "name":
                    queryWrapper.orderBy(true, isAsc, EmPatient::getName);
                    break;
                case "age":
                    queryWrapper.orderBy(true, isAsc, EmPatient::getAge);
                    break;
                case "createdAt":
                    queryWrapper.orderBy(true, isAsc, EmPatient::getCreatedAt);
                    break;
                case "updatedAt":
                    queryWrapper.orderBy(true, isAsc, EmPatient::getUpdatedAt);
                    break;
                default:
                    queryWrapper.orderByDesc(EmPatient::getCreatedAt);
                    break;
            }
        } else {
            queryWrapper.orderByDesc(EmPatient::getCreatedAt);
        }
    }

    /**
     * 转换为响应DTO
     */
    private PatientResponseDTO convertToResponseDTO(EmPatient patient) {
        PatientResponseDTO responseDTO = new PatientResponseDTO();
        BeanUtils.copyProperties(patient, responseDTO);
        
        // 设置性别描述
        if (patient.getGender() != null) {
            responseDTO.setGenderDesc(patient.getGender() == 1 ? "男" : "女");
        }
        
        // 设置患者类型描述
        if (patient.getPatientType() != null) {
            responseDTO.setPatientTypeDesc(patient.getPatientType() == 1 ? "阳性" : "阴性");
        }
        
        return responseDTO;
    }
}
