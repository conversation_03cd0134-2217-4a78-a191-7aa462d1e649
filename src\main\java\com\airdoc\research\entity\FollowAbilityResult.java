package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 追随能力检测结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("follow_ability_results")
public class FollowAbilityResult extends BaseEntity {

    /**
     * 结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 目标路径点(贝塞尔曲线)
     */
    private String followPathPoints;

    /**
     * 实际视线点
     */
    private String actualGazePoints;

    /**
     * 跟踪准确率(%)
     */
    private BigDecimal trackingAccuracy;

    /**
     * 平均误差距离
     */
    private BigDecimal averageError;

    /**
     * 反应时间(ms)
     */
    private BigDecimal reactionTime;

    /**
     * 平滑度评分
     */
    private BigDecimal smoothnessScore;

    /**
     * 完成率(%)
     */
    private BigDecimal completionRate;

    /**
     * 贝塞尔曲线控制点数据
     */
    private String bezierCurveData;

    /**
     * 追随次数
     */
    private Integer followCount;

    /**
     * 成功追随次数
     */
    private Integer successfulFollows;

    /**
     * 路径总长度(px)
     */
    private BigDecimal pathLength;

    /**
     * 实际追随路径长度(px)
     */
    private BigDecimal actualPathLength;

    /**
     * 速度一致性评分
     */
    private BigDecimal velocityConsistency;
}
