package com.airdoc.research.service;

import com.airdoc.research.mapper.EmPatientMapper;
import com.airdoc.research.mapper.SysUserMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 现代化依赖注入服务类
 * 展示Spring Boot 3最推荐的构造器注入方式（不使用Lombok）
 */
@Slf4j
@Service
public class ModernDependencyInjectionService {

    // 使用final字段确保依赖不可变
    private final EmPatientMapper patientMapper;
    private final SysUserMapper userMapper;
    private final ObjectMapper objectMapper;

    /**
     * 构造器注入（Spring Boot 3最推荐的方式）
     * 当只有一个构造器时，@Autowired注解可以省略
     * 
     * 优点：
     * 1. 依赖不可变（final字段）
     * 2. 确保依赖在对象创建时就完全初始化
     * 3. 便于单元测试
     * 4. 避免循环依赖
     * 5. 符合依赖注入的最佳实践
     */
    public ModernDependencyInjectionService(
            EmPatientMapper patientMapper,
            SysUserMapper userMapper,
            ObjectMapper objectMapper) {
        this.patientMapper = patientMapper;
        this.userMapper = userMapper;
        this.objectMapper = objectMapper;
        
        log.info("ModernDependencyInjectionService 构造器注入完成");
    }

    /**
     * 示例方法：展示构造器注入的依赖使用
     */
    public void demonstrateConstructorInjection() {
        log.info("使用构造器注入的依赖:");
        log.info("PatientMapper: {}", patientMapper != null ? "已注入" : "未注入");
        log.info("UserMapper: {}", userMapper != null ? "已注入" : "未注入");
        log.info("ObjectMapper: {}", objectMapper != null ? "已注入" : "未注入");
    }

    /**
     * 获取注入状态信息
     */
    public String getInjectionStatus() {
        return String.format("构造器注入状态 - PatientMapper: %s, UserMapper: %s, ObjectMapper: %s",
                patientMapper != null ? "✓" : "✗",
                userMapper != null ? "✓" : "✗",
                objectMapper != null ? "✓" : "✗");
    }

    /**
     * 演示依赖的实际使用
     */
    public void demonstrateUsage() {
        try {
            // 这里可以安全地使用依赖，因为它们在构造时就已经初始化
            log.info("PatientMapper类型: {}", patientMapper.getClass().getSimpleName());
            log.info("UserMapper类型: {}", userMapper.getClass().getSimpleName());
            log.info("ObjectMapper类型: {}", objectMapper.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("使用依赖时发生错误", e);
        }
    }
}
