package com.airdoc.research.controller;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.common.Result;
import com.airdoc.research.dto.*;
import com.airdoc.research.service.EmPatientService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 患者管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/patients")
@Validated
public class EmPatientController {

    @Resource
    private EmPatientService patientService;

    /**
     * 创建患者
     */
    @PostMapping
    public Result<PatientIdResponseDTO> createPatient(@Valid @RequestBody PatientCreateDTO createDTO) {
        log.info("创建患者请求，姓名：{}", createDTO.getName());

        try {
            PatientIdResponseDTO responseDTO = patientService.createPatient(createDTO);
            return Result.success("患者创建成功", responseDTO);
        } catch (Exception e) {
            log.error("创建患者失败", e);
            return Result.error("创建患者失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取患者信息
     */
    @GetMapping("/{id}")
    public Result<PatientResponseDTO> getPatientById(@PathVariable Long id) {
        log.debug("根据ID查询患者：{}", id);
        
        try {
            PatientResponseDTO responseDTO = patientService.getPatientById(id);
            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("查询患者失败，ID：{}", id, e);
            return Result.error("查询患者失败：" + e.getMessage());
        }
    }

    /**
     * 根据UUID获取患者信息
     */
    @GetMapping("/uuid/{patientUuid}")
    public Result<PatientResponseDTO> getPatientByUuid(@PathVariable String patientUuid) {
        log.debug("根据UUID查询患者：{}", patientUuid);
        
        try {
            PatientResponseDTO responseDTO = patientService.getPatientByUuid(patientUuid);
            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("查询患者失败，UUID：{}", patientUuid, e);
            return Result.error("查询患者失败：" + e.getMessage());
        }
    }

    /**
     * 更新患者信息
     */
    @PutMapping
    public Result<PatientResponseDTO> updatePatient(@Valid @RequestBody PatientUpdateDTO updateDTO) {
        log.info("更新患者请求，ID：{}", updateDTO.getId());
        
        try {
            PatientResponseDTO responseDTO = patientService.updatePatient(updateDTO);
            return Result.success("患者信息更新成功", responseDTO);
        } catch (Exception e) {
            log.error("更新患者失败，ID：{}", updateDTO.getId(), e);
            return Result.error("更新患者失败：" + e.getMessage());
        }
    }

    /**
     * 删除患者
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deletePatient(@PathVariable Long id) {
        log.info("删除患者请求，ID：{}", id);
        
        try {
            Boolean result = patientService.deletePatient(id);
            return Result.success("患者删除成功", result);
        } catch (Exception e) {
            log.error("删除患者失败，ID：{}", id, e);
            return Result.error("删除患者失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除患者
     */
    @DeleteMapping("/batch")
    public Result<Integer> batchDeletePatients(@RequestBody List<Long> ids) {
        log.info("批量删除患者请求，数量：{}", ids.size());
        
        try {
            Integer result = patientService.batchDeletePatients(ids);
            return Result.success("批量删除成功", result);
        } catch (Exception e) {
            log.error("批量删除患者失败", e);
            return Result.error("批量删除患者失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询患者
     */
    @PostMapping("/page")
    public Result<PageResponse<PatientResponseDTO>> pageQueryPatients(@RequestBody PatientPageQueryDTO queryDTO) {
        log.debug("分页查询患者，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());
        
        try {
            PageResponse<PatientResponseDTO> pageResponse = patientService.pageQueryPatients(queryDTO);
            return Result.success(pageResponse);
        } catch (Exception e) {
            log.error("分页查询患者失败", e);
            return Result.error("分页查询患者失败：" + e.getMessage());
        }
    }

    /**
     * 查询患者列表
     */
    @PostMapping("/list")
    public Result<List<PatientResponseDTO>> queryPatients(@RequestBody PatientQueryDTO queryDTO) {
        log.debug("查询患者列表");
        
        try {
            List<PatientResponseDTO> patients = patientService.queryPatients(queryDTO);
            return Result.success(patients);
        } catch (Exception e) {
            log.error("查询患者列表失败", e);
            return Result.error("查询患者列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查患者是否存在
     */
    @GetMapping("/{id}/exists")
    public Result<Boolean> existsById(@PathVariable Long id) {
        try {
            Boolean exists = patientService.existsById(id);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查患者是否存在失败，ID：{}", id, e);
            return Result.error("检查患者是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 根据住院号查询患者
     */
    @GetMapping("/inpatient/{inpatientNum}")
    public Result<PatientResponseDTO> getPatientByInpatientNum(@PathVariable String inpatientNum) {
        log.debug("根据住院号查询患者：{}", inpatientNum);
        
        try {
            PatientResponseDTO responseDTO = patientService.getPatientByInpatientNum(inpatientNum);
            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("根据住院号查询患者失败：{}", inpatientNum, e);
            return Result.error("查询患者失败：" + e.getMessage());
        }
    }

    /**
     * 根据病例卡号查询患者
     */
    @GetMapping("/case-card/{caseCardNum}")
    public Result<PatientResponseDTO> getPatientByCaseCardNum(@PathVariable String caseCardNum) {
        log.debug("根据病例卡号查询患者：{}", caseCardNum);
        
        try {
            PatientResponseDTO responseDTO = patientService.getPatientByCaseCardNum(caseCardNum);
            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("根据病例卡号查询患者失败：{}", caseCardNum, e);
            return Result.error("查询患者失败：" + e.getMessage());
        }
    }

    /**
     * 统计患者总数
     */
    @GetMapping("/count")
    public Result<Long> countPatients() {
        try {
            Long count = patientService.countPatients();
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计患者总数失败", e);
            return Result.error("统计患者总数失败：" + e.getMessage());
        }
    }

    /**
     * 根据机构ID统计患者数量
     */
    @GetMapping("/count/organization/{organizationId}")
    public Result<Long> countPatientsByOrganization(@PathVariable String organizationId) {
        try {
            Long count = patientService.countPatientsByOrganization(organizationId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("根据机构统计患者数量失败，机构ID：{}", organizationId, e);
            return Result.error("统计患者数量失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("患者管理服务运行正常");
    }
}
