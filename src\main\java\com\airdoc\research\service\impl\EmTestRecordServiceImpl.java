package com.airdoc.research.service.impl;

import com.airdoc.research.entity.EmTestRecord;
import com.airdoc.research.mapper.EmTestRecordMapper;
import com.airdoc.research.service.EmTestRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检测记录服务实现类
 */
@Slf4j
@Service
public class EmTestRecordServiceImpl implements EmTestRecordService {

    @Resource
    private EmTestRecordMapper testRecordMapper;

    @Override
    public EmTestRecord getById(Long id) {
        log.debug("根据ID查询检测记录：{}", id);
        return testRecordMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateImageUrl(Long recordId, String imageUrl) {
        log.info("更新检测记录图片URL，记录ID：{}，图片URL：{}", recordId, imageUrl);

        try {
            // 检查记录是否存在
            EmTestRecord existingRecord = testRecordMapper.selectById(recordId);
            if (existingRecord == null) {
                log.warn("检测记录不存在，ID：{}", recordId);
                return false;
            }

            // 更新图片URL
            existingRecord.setImageUrl(imageUrl);
            int updateCount = testRecordMapper.updateById(existingRecord);

            if (updateCount > 0) {
                log.info("检测记录图片URL更新成功，记录ID：{}", recordId);
                return true;
            } else {
                log.warn("检测记录图片URL更新失败，记录ID：{}", recordId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新检测记录图片URL失败，记录ID：{}", recordId, e);
            throw new RuntimeException("更新检测记录图片URL失败：" + e.getMessage());
        }
    }

    @Override
    public boolean existsById(Long recordId) {
        if (recordId == null) {
            return false;
        }
        return testRecordMapper.selectById(recordId) != null;
    }
}
