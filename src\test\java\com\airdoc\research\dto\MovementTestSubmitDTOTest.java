//package com.airdoc.research.dto;
//
//import org.junit.jupiter.api.Test;
//import static org.junit.jupiter.api.Assertions.*;
//
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * MovementTestSubmitDTO测试类
// * 验证修改后的DTO结构是否正确
// */
//public class MovementTestSubmitDTOTest {
//
//    @Test
//    public void testMovementTestSubmitDTOStructure() {
//        // 创建测试DTO
//        MovementTestSubmitDTO submitDTO = new MovementTestSubmitDTO();
//
//        // 测试患者ID字段
//        Long patientId = 12345L;
//        submitDTO.setPatientId(patientId);
//        assertEquals(patientId, submitDTO.getPatientId());
//
//        // 测试设备信息
//        MovementTestSubmitDTO.DeviceInfo deviceInfo = new MovementTestSubmitDTO.DeviceInfo();
//        deviceInfo.setDeviceSn("TEST_DEVICE_001");
//        deviceInfo.setDeviceName("测试设备");
//        submitDTO.setDeviceInfo(deviceInfo);
//        assertEquals("TEST_DEVICE_001", submitDTO.getDeviceInfo().getDeviceSn());
//
//        // 测试操作员信息
//        MovementTestSubmitDTO.OperatorInfo operatorInfo = new MovementTestSubmitDTO.OperatorInfo();
//        operatorInfo.setUsername("test_operator");
//        operatorInfo.setAccountName("测试操作员");
//        submitDTO.setOperatorInfo(operatorInfo);
//        assertEquals("test_operator", submitDTO.getOperatorInfo().getUsername());
//
//        // 测试检测信息
//        MovementTestSubmitDTO.TestInfo testInfo = new MovementTestSubmitDTO.TestInfo();
//        testInfo.setTestType("GAZE_STABILITY");
//        testInfo.setTestSequence("01");
//        testInfo.setTestDate(LocalDateTime.now());
//        submitDTO.setTestInfo(testInfo);
//        assertEquals("GAZE_STABILITY", submitDTO.getTestInfo().getTestType());
//
//        // 测试结果数据
//        MovementTestSubmitDTO.TestResultData resultData = new MovementTestSubmitDTO.TestResultData();
//
//        // 创建视线轨迹数据
//        List<MovementTestSubmitDTO.GazePoint> gazeTrajectory = new ArrayList<>();
//        MovementTestSubmitDTO.GazePoint gazePoint = new MovementTestSubmitDTO.GazePoint();
//        gazePoint.setIndex(1);
//        gazePoint.setX(new BigDecimal("100.5"));
//        gazePoint.setY(new BigDecimal("200.5"));
//        gazePoint.setIsValid(true);
//        gazeTrajectory.add(gazePoint);
//        resultData.setGazeTrajectory(gazeTrajectory);
//
//        // 创建注视稳定性数据
//        MovementTestSubmitDTO.GazeStabilityData gazeStabilityData = new MovementTestSubmitDTO.GazeStabilityData();
//        gazeStabilityData.setTargetX(new BigDecimal("150.0"));
//        gazeStabilityData.setTargetY(new BigDecimal("250.0"));
//        gazeStabilityData.setStabilityScore(new BigDecimal("85.5"));
//        resultData.setGazeStabilityData(gazeStabilityData);
//
//        submitDTO.setResultData(resultData);
//
//        // 验证结果数据
//        assertNotNull(submitDTO.getResultData());
//        assertEquals(1, submitDTO.getResultData().getGazeTrajectory().size());
//        assertEquals(new BigDecimal("85.5"), submitDTO.getResultData().getGazeStabilityData().getStabilityScore());
//
//        System.out.println("MovementTestSubmitDTO结构测试通过！");
//        System.out.println("患者ID: " + submitDTO.getPatientId());
//        System.out.println("设备序列号: " + submitDTO.getDeviceInfo().getDeviceSn());
//        System.out.println("操作员用户名: " + submitDTO.getOperatorInfo().getUsername());
//        System.out.println("检测类型: " + submitDTO.getTestInfo().getTestType());
//    }
//
//    @Test
//    public void testPatientIdValidation() {
//        MovementTestSubmitDTO submitDTO = new MovementTestSubmitDTO();
//
//        // 测试设置和获取患者ID
//        Long testPatientId = 999L;
//        submitDTO.setPatientId(testPatientId);
//
//        assertNotNull(submitDTO.getPatientId());
//        assertEquals(testPatientId, submitDTO.getPatientId());
//
//        System.out.println("患者ID验证测试通过！");
//    }
//}
