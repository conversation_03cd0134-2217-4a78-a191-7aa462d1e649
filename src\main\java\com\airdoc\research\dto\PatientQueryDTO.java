package com.airdoc.research.dto;

import lombok.Data;

import java.time.LocalDate;

/**
 * 患者查询DTO
 */
@Data
public class PatientQueryDTO {

    /**
     * 患者姓名（模糊查询）
     */
    private String name;

    /**
     * 住院号
     */
    private String inpatientNum;

    /**
     * 病例卡号
     */
    private String caseCardNum;

    /**
     * 患者UUID
     */
    private String patientUuid;

    /**
     * 性别 1=男 2=女
     */
    private Integer gender;

    /**
     * 患者类型 1=阳性 2=阴性
     */
    private Integer patientType;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 机构ID
     */
    private String organizationId;

    /**
     * 三方患者编号
     */
    private String thirdBizId;

    /**
     * 年龄范围 - 最小年龄
     */
    private Integer minAge;

    /**
     * 年龄范围 - 最大年龄
     */
    private Integer maxAge;

    /**
     * 出生日期范围 - 开始日期
     */
    private LocalDate birthdayStart;

    /**
     * 出生日期范围 - 结束日期
     */
    private LocalDate birthdayEnd;

    /**
     * 创建时间范围 - 开始时间
     */
    private LocalDate createdAtStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private LocalDate createdAtEnd;
}
