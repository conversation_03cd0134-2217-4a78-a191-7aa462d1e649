package com.airdoc.research.controller;

import com.airdoc.research.common.Result;
import com.airdoc.research.service.DependencyInjectionExampleService;
import com.airdoc.research.service.ModernDependencyInjectionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 依赖注入演示控制器
 * 展示Spring Boot 3推荐的依赖注入方式
 */
@Slf4j
@RestController
@RequestMapping("/demo/dependency-injection")
public class DependencyInjectionDemoController {

    // 方式1: 使用@Resource注解（JSR-250标准）
    @Resource
    private DependencyInjectionExampleService resourceExampleService;

    // 方式2: 构造器注入（最推荐的方式）
    private final ModernDependencyInjectionService modernService;

    /**
     * 构造器注入（Spring Boot 3最推荐的方式）
     * 当只有一个构造器时，@Autowired注解可以省略
     */
    public DependencyInjectionDemoController(ModernDependencyInjectionService modernService) {
        this.modernService = modernService;
        log.info("DependencyInjectionDemoController 构造器注入完成");
    }

    /**
     * 演示@Resource注入方式
     */
    @GetMapping("/resource-demo")
    public Result<String> demonstrateResourceInjection() {
        log.info("演示@Resource注入方式");
        
        resourceExampleService.demonstrateResourceInjection();
        String status = resourceExampleService.getInjectionStatus();
        
        return Result.success("@Resource注入演示完成", status);
    }

    /**
     * 演示构造器注入方式
     */
    @GetMapping("/constructor-demo")
    public Result<String> demonstrateConstructorInjection() {
        log.info("演示构造器注入方式");
        
        modernService.demonstrateConstructorInjection();
        modernService.demonstrateUsage();
        String status = modernService.getInjectionStatus();
        
        return Result.success("构造器注入演示完成", status);
    }

    /**
     * 比较不同注入方式
     */
    @GetMapping("/comparison")
    public Result<Map<String, Object>> compareInjectionMethods() {
        log.info("比较不同的依赖注入方式");
        
        Map<String, Object> comparison = new HashMap<>();
        
        // @Resource注入状态
        comparison.put("resourceInjection", resourceExampleService.getInjectionStatus());
        
        // 构造器注入状态
        comparison.put("constructorInjection", modernService.getInjectionStatus());
        
        // 推荐说明
        Map<String, String> recommendations = new HashMap<>();
        recommendations.put("@Resource", "JSR-250标准，Spring Boot 3支持，适合简单场景");
        recommendations.put("构造器注入", "Spring Boot 3最推荐，依赖不可变，便于测试");
        recommendations.put("@Autowired", "Spring特有，仍然支持但不是最佳实践");
        
        comparison.put("recommendations", recommendations);
        
        return Result.success("依赖注入方式比较", comparison);
    }

    /**
     * 获取Spring Boot 3依赖注入最佳实践
     */
    @GetMapping("/best-practices")
    public Result<Map<String, Object>> getBestPractices() {
        Map<String, Object> bestPractices = new HashMap<>();
        
        bestPractices.put("首选", "构造器注入 - 依赖不可变，便于测试");
        bestPractices.put("备选", "@Resource - JSR-250标准，简洁明了");
        bestPractices.put("避免", "字段注入@Autowired - 虽然仍然支持，但不推荐");
        
        Map<String, String> reasons = new HashMap<>();
        reasons.put("构造器注入优点", "1.依赖不可变 2.确保完全初始化 3.便于单元测试 4.避免循环依赖");
        reasons.put("@Resource优点", "1.JSR-250标准 2.按名称注入 3.代码简洁");
        reasons.put("字段注入缺点", "1.依赖可变 2.难以测试 3.可能循环依赖");
        
        bestPractices.put("详细说明", reasons);
        
        return Result.success("Spring Boot 3依赖注入最佳实践", bestPractices);
    }
}
