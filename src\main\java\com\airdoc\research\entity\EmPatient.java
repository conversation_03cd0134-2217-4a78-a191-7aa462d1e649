package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 患者信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("em_patients")
public class EmPatient extends BaseEntity {

    /**
     * 患者ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 患者UUID
     */
    private String patientUuid;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 住院号
     */
    private String inpatientNum;

    /**
     * 病例卡号
     */
    private String caseCardNum;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别 1=男 2=女
     */
    private Integer gender;

    /**
     * 患者类型 1=阳性 2=阴性
     */
    private Integer patientType;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号国家代码
     */
    private Integer phoneCountryCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 患者个性签名
     */
    private String description;

    /**
     * 诊断信息
     */
    private String diagnosisInformation;

    /**
     * 机构ID
     */
    private String organizationId;

    /**
     * 三方患者编号
     */
    private String thirdBizId;
}
