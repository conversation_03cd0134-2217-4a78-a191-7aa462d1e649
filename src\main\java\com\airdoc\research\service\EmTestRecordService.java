package com.airdoc.research.service;

import com.airdoc.research.entity.EmTestRecord;

/**
 * 检测记录服务接口
 */
public interface EmTestRecordService {

    /**
     * 根据ID获取检测记录
     * @param id 记录ID
     * @return 检测记录
     */
    EmTestRecord getById(Long id);

    /**
     * 更新检测记录的图片URL
     * @param recordId 记录ID
     * @param imageUrl 图片URL
     * @return 是否更新成功
     */
    boolean updateImageUrl(Long recordId, String imageUrl);

    /**
     * 检查记录是否存在
     * @param recordId 记录ID
     * @return 是否存在
     */
    boolean existsById(Long recordId);
}
