package com.airdoc.research.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 扫视能力检测查询DTO
 */
@Data
public class SaccadeAbilityQueryDTO {

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名（模糊查询）
     */
    private String patientName;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员姓名（模糊查询）
     */
    private String operatorName;

    /**
     * 检测状态
     */
    private String status;

    /**
     * 准确率范围 - 最小值(%)
     */
    private BigDecimal minAccuracyRate;

    /**
     * 准确率范围 - 最大值(%)
     */
    private BigDecimal maxAccuracyRate;

    /**
     * 检测日期范围 - 开始日期
     */
    private LocalDate testDateStart;

    /**
     * 检测日期范围 - 结束日期
     */
    private LocalDate testDateEnd;

    /**
     * 检测时长范围 - 最小时长(秒)
     */
    private Integer minDuration;

    /**
     * 检测时长范围 - 最大时长(秒)
     */
    private Integer maxDuration;

    /**
     * 总扫视次数范围 - 最小值
     */
    private Integer minTotalSaccades;

    /**
     * 总扫视次数范围 - 最大值
     */
    private Integer maxTotalSaccades;

    /**
     * 成功扫视次数范围 - 最小值
     */
    private Integer minSuccessfulSaccades;

    /**
     * 成功扫视次数范围 - 最大值
     */
    private Integer maxSuccessfulSaccades;

    /**
     * 平均扫视时间范围 - 最小值(ms)
     */
    private BigDecimal minAverageSaccadeTime;

    /**
     * 平均扫视时间范围 - 最大值(ms)
     */
    private BigDecimal maxAverageSaccadeTime;

    /**
     * 扫视速度范围 - 最小值(度/秒)
     */
    private BigDecimal minSaccadeVelocity;

    /**
     * 扫视速度范围 - 最大值(度/秒)
     */
    private BigDecimal maxSaccadeVelocity;

    /**
     * 误差距离范围 - 最小值
     */
    private BigDecimal minErrorDistance;

    /**
     * 误差距离范围 - 最大值
     */
    private BigDecimal maxErrorDistance;

    /**
     * 扫视潜伏期范围 - 最小值(ms)
     */
    private BigDecimal minLatency;

    /**
     * 扫视潜伏期范围 - 最大值(ms)
     */
    private BigDecimal maxLatency;

    /**
     * 峰值速度范围 - 最小值(度/秒)
     */
    private BigDecimal minPeakVelocity;

    /**
     * 峰值速度范围 - 最大值(度/秒)
     */
    private BigDecimal maxPeakVelocity;

    /**
     * 欠冲率范围 - 最小值(%)
     */
    private BigDecimal minUndershootRate;

    /**
     * 欠冲率范围 - 最大值(%)
     */
    private BigDecimal maxUndershootRate;

    /**
     * 过冲率范围 - 最小值(%)
     */
    private BigDecimal minOvershootRate;

    /**
     * 过冲率范围 - 最大值(%)
     */
    private BigDecimal maxOvershootRate;

    /**
     * 注视稳定性评分范围 - 最小值
     */
    private BigDecimal minFixationStability;

    /**
     * 注视稳定性评分范围 - 最大值
     */
    private BigDecimal maxFixationStability;

    /**
     * 创建时间范围 - 开始时间
     */
    private LocalDateTime createdAtStart;

    /**
     * 创建时间范围 - 结束时间
     */
    private LocalDateTime createdAtEnd;
}
