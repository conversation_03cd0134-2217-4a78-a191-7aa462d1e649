package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 扫视能力检测结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("saccade_ability_results")
public class SaccadeAbilityResult extends BaseEntity {

    /**
     * 结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 目标点序列
     */
    private String targetPoints;

    /**
     * 扫视事件详情
     */
    private String saccadeEvents;

    /**
     * 视线轨迹JSON数据
     */
    private String gazeTrajectoryJson;

    /**
     * 总扫视次数
     */
    private Integer totalSaccades;

    /**
     * 成功扫视次数
     */
    private Integer successfulSaccades;

    /**
     * 准确率(%)
     */
    private BigDecimal accuracyRate;

    /**
     * 平均扫视时间(ms)
     */
    private BigDecimal averageSaccadeTime;

    /**
     * 平均扫视速度(度/秒)
     */
    private BigDecimal saccadeVelocity;

    /**
     * 平均误差距离
     */
    private BigDecimal errorDistance;

    /**
     * 扫视潜伏期(ms)
     */
    private BigDecimal latency;

    /**
     * 峰值速度(度/秒)
     */
    private BigDecimal peakVelocity;

    /**
     * 欠冲率(%)
     */
    private BigDecimal undershootRate;

    /**
     * 过冲率(%)
     */
    private BigDecimal overshootRate;

    /**
     * 注视稳定性评分
     */
    private BigDecimal fixationStability;
}
