package com.airdoc.research.dto;

import lombok.Data;

/**
 * 文件上传响应DTO
 */
@Data
public class FileUploadResponseDTO {

    /**
     * 文件URL
     */
    private String url;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 存储文件名
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    public FileUploadResponseDTO() {
    }

    public FileUploadResponseDTO(String url, String originalFileName, String fileName, Long fileSize, String contentType) {
        this.url = url;
        this.originalFileName = originalFileName;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
    }
}
