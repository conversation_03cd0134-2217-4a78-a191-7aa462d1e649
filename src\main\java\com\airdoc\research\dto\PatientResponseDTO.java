package com.airdoc.research.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者响应DTO
 */
@Data
public class PatientResponseDTO {

    /**
     * 患者ID
     */
    private Long id;

    /**
     * 患者UUID
     */
    private String patientUuid;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 住院号
     */
    private String inpatientNum;

    /**
     * 病例卡号
     */
    private String caseCardNum;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别 1=男 2=女
     */
    private Integer gender;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 患者类型 1=阳性 2=阴性
     */
    private Integer patientType;

    /**
     * 患者类型描述
     */
    private String patientTypeDesc;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 手机号国家代码
     */
    private Integer phoneCountryCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 患者个性签名
     */
    private String description;

    /**
     * 诊断信息
     */
    private String diagnosisInformation;

    /**
     * 机构ID
     */
    private String organizationId;

    /**
     * 三方患者编号
     */
    private String thirdBizId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 修改人ID
     */
    private Long updatedBy;
}
