package com.airdoc.research.controller;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.common.Result;
import com.airdoc.research.dto.*;
import com.airdoc.research.service.MovementTestService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;



/**
 * Movement检测结果提交控制器
 * 使用@Resource注解进行依赖注入（Spring Boot 3推荐方式）
 */
@Slf4j
@RestController
@RequestMapping("/movement")
@Validated
public class MovementTestController {

    // 使用@Resource注解进行依赖注入（JSR-250标准，Spring Boot 3推荐）
    @Resource
    private MovementTestService movementTestService;

    /**
     * 提交注视稳定性检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/gaze-stability/submit")
    public Result<Long> submitGazeStabilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                  @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到注视稳定性检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"GAZE_STABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：GAZE_STABILITY");
            }
            
            if (!"01".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：01");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("注视稳定性检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("注视稳定性检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交追随能力检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/follow-ability/submit")
    public Result<Long> submitFollowAbilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                  @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到追随能力检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"FOLLOW_ABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：FOLLOW_ABILITY");
            }
            
            if (!"02".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：02");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("追随能力检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("追随能力检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/saccade-ability/submit")
    public Result<Long> submitSaccadeAbilityResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                   @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到扫视能力检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"SACCADE_ABILITY".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：SACCADE_ABILITY");
            }
            
            if (!"03".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：03");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("扫视能力检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("扫视能力检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交ROI检测结果
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/roi-detection/submit")
    public Result<Long> submitRoiDetectionResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                                 @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到ROI检测结果提交请求");
        
        try {
            // 验证检测类型
            if (!"ROI_DETECTION".equals(submitDTO.getTestInfo().getTestType())) {
                return Result.badRequest("检测类型不匹配，期望：ROI_DETECTION");
            }
            
            if (!"04".equals(submitDTO.getTestInfo().getTestSequence())) {
                return Result.badRequest("检测序列号不匹配，期望：04");
            }
            
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("ROI检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("ROI检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 通用提交接口（支持所有检测类型）
     * @param submitDTO 检测结果数据
     * @return 提交结果
     */
    @PostMapping("/submit")
    public Result<Long> submitTestResult(@Valid @RequestBody MovementTestSubmitDTO submitDTO,
                                         @RequestHeader("X-Device-Sn") String deviceSn) {
        log.info("接收到Movement检测结果提交请求，类型：{}", submitDTO.getTestInfo().getTestType());
        
        try {
            Long recordId = movementTestService.submitTestResult(submitDTO, deviceSn);
            return Result.success("检测结果提交成功", recordId);
            
        } catch (Exception e) {
            log.error("检测结果提交失败", e);
            return Result.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     * @return 健康状态
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("Movement检测服务运行正常");
    }

    // ========== 注视稳定性测试查询接口 ==========

    /**
     * 分页查询注视稳定性测试列表
     * @param queryDTO 分页查询参数
     * @return 分页响应结果
     */
    @PostMapping("/gaze-stability/page")
    public Result<PageResponse<GazeStabilityResponseDTO>> pageQueryGazeStabilityTests(@RequestBody GazeStabilityPageQueryDTO queryDTO) {
        log.debug("分页查询注视稳定性测试，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());

        try {
            PageResponse<GazeStabilityResponseDTO> pageResponse = movementTestService.pageQueryGazeStabilityTests(queryDTO);
            return Result.success(pageResponse);
        } catch (Exception e) {
            log.error("分页查询注视稳定性测试失败", e);
            return Result.error("分页查询注视稳定性测试失败：" + e.getMessage());
        }
    }

    /**
     * 查询注视稳定性测试列表
     * @param queryDTO 查询参数
     * @return 测试列表
     */
    @PostMapping("/gaze-stability/list")
    public Result<List<GazeStabilityResponseDTO>> queryGazeStabilityTests(@RequestBody GazeStabilityQueryDTO queryDTO) {
        log.debug("查询注视稳定性测试列表");

        try {
            List<GazeStabilityResponseDTO> tests = movementTestService.queryGazeStabilityTests(queryDTO);
            return Result.success(tests);
        } catch (Exception e) {
            log.error("查询注视稳定性测试列表失败", e);
            return Result.error("查询注视稳定性测试列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询注视稳定性测试详情
     * @param id 结果ID
     * @return 测试详情
     */
    @GetMapping("/gaze-stability/{id}")
    public Result<GazeStabilityResponseDTO> getGazeStabilityTestById(@PathVariable Long id) {
        log.debug("根据ID查询注视稳定性测试详情：{}", id);

        try {
            GazeStabilityResponseDTO test = movementTestService.getGazeStabilityTestById(id);
            return Result.success(test);
        } catch (Exception e) {
            log.error("根据ID查询注视稳定性测试详情失败：{}", id, e);
            return Result.error("查询注视稳定性测试详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据记录ID查询注视稳定性测试详情
     * @param recordId 检测记录ID
     * @return 测试详情
     */
    @GetMapping("/gaze-stability/record/{recordId}")
    public Result<GazeStabilityResponseDTO> getGazeStabilityTestByRecordId(@PathVariable Long recordId) {
        log.debug("根据记录ID查询注视稳定性测试详情：{}", recordId);

        try {
            GazeStabilityResponseDTO test = movementTestService.getGazeStabilityTestByRecordId(recordId);
            return Result.success(test);
        } catch (Exception e) {
            log.error("根据记录ID查询注视稳定性测试详情失败：{}", recordId, e);
            return Result.error("查询注视稳定性测试详情失败：" + e.getMessage());
        }
    }

    /**
     * 统计注视稳定性测试数量
     * @param queryDTO 查询参数
     * @return 测试数量
     */
    @PostMapping("/gaze-stability/count")
    public Result<Long> countGazeStabilityTests(@RequestBody GazeStabilityQueryDTO queryDTO) {
        log.debug("统计注视稳定性测试数量");

        try {
            Long count = movementTestService.countGazeStabilityTests(queryDTO);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计注视稳定性测试数量失败", e);
            return Result.error("统计注视稳定性测试数量失败：" + e.getMessage());
        }
    }

    // ========== 扫视能力检测查询接口 ==========

    /**
     * 分页查询扫视能力检测列表
     * @param queryDTO 分页查询参数
     * @return 分页响应结果
     */
    @PostMapping("/saccade-ability/page")
    public Result<PageResponse<SaccadeAbilityResponseDTO>> pageQuerySaccadeAbilityTests(@RequestBody SaccadeAbilityPageQueryDTO queryDTO) {
        log.debug("分页查询扫视能力检测，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());

        try {
            PageResponse<SaccadeAbilityResponseDTO> pageResponse = movementTestService.pageQuerySaccadeAbilityTests(queryDTO);
            return Result.success(pageResponse);
        } catch (Exception e) {
            log.error("分页查询扫视能力检测失败", e);
            return Result.error("分页查询扫视能力检测失败：" + e.getMessage());
        }
    }

    /**
     * 查询扫视能力检测列表
     * @param queryDTO 查询参数
     * @return 测试列表
     */
    @PostMapping("/saccade-ability/list")
    public Result<List<SaccadeAbilityResponseDTO>> querySaccadeAbilityTests(@RequestBody SaccadeAbilityQueryDTO queryDTO) {
        log.debug("查询扫视能力检测列表");

        try {
            List<SaccadeAbilityResponseDTO> tests = movementTestService.querySaccadeAbilityTests(queryDTO);
            return Result.success(tests);
        } catch (Exception e) {
            log.error("查询扫视能力检测列表失败", e);
            return Result.error("查询扫视能力检测列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询扫视能力检测详情
     * @param id 结果ID
     * @return 测试详情
     */
    @GetMapping("/saccade-ability/{id}")
    public Result<SaccadeAbilityResponseDTO> getSaccadeAbilityTestById(@PathVariable Long id) {
        log.debug("根据ID查询扫视能力检测详情：{}", id);

        try {
            SaccadeAbilityResponseDTO test = movementTestService.getSaccadeAbilityTestById(id);
            return Result.success(test);
        } catch (Exception e) {
            log.error("根据ID查询扫视能力检测详情失败：{}", id, e);
            return Result.error("查询扫视能力检测详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据记录ID查询扫视能力检测详情
     * @param recordId 检测记录ID
     * @return 测试详情
     */
    @GetMapping("/saccade-ability/record/{recordId}")
    public Result<SaccadeAbilityResponseDTO> getSaccadeAbilityTestByRecordId(@PathVariable Long recordId) {
        log.debug("根据记录ID查询扫视能力检测详情：{}", recordId);

        try {
            SaccadeAbilityResponseDTO test = movementTestService.getSaccadeAbilityTestByRecordId(recordId);
            return Result.success(test);
        } catch (Exception e) {
            log.error("根据记录ID查询扫视能力检测详情失败：{}", recordId, e);
            return Result.error("查询扫视能力检测详情失败：" + e.getMessage());
        }
    }

    /**
     * 统计扫视能力检测数量
     * @param queryDTO 查询参数
     * @return 测试数量
     */
    @PostMapping("/saccade-ability/count")
    public Result<Long> countSaccadeAbilityTests(@RequestBody SaccadeAbilityQueryDTO queryDTO) {
        log.debug("统计扫视能力检测数量");

        try {
            Long count = movementTestService.countSaccadeAbilityTests(queryDTO);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计扫视能力检测数量失败", e);
            return Result.error("统计扫视能力检测数量失败：" + e.getMessage());
        }
    }
}
