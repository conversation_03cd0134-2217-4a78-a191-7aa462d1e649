package com.airdoc.research.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置类
 */
@Configuration
@MapperScan("com.airdoc.research.mapper")
public class MybatisPlusConfig {

    /**
     * MyBatis Plus插件配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
//        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        return interceptor;
    }

    /**
     * 自动填充配置
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                LocalDateTime now = LocalDateTime.now();
                
                // 创建时间
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
                // 修改时间
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
                // 创建人ID（这里设置为1，实际项目中应该从当前登录用户获取）
                this.strictInsertFill(metaObject, "createdBy", Long.class, 1L);
                // 修改人ID
                this.strictInsertFill(metaObject, "updatedBy", Long.class, 1L);
                // 逻辑删除标记
                this.strictInsertFill(metaObject, "isDeleted", Boolean.class, false);
                // 版本号
                this.strictInsertFill(metaObject, "version", Integer.class, 1);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                // 修改时间
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
                // 修改人ID（这里设置为1，实际项目中应该从当前登录用户获取）
                this.strictUpdateFill(metaObject, "updatedBy", Long.class, 1L);
            }
        };
    }
}
