package com.airdoc.research.service;

import com.airdoc.research.dto.FileUploadResponseDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 */
public interface FileUploadService {

    /**
     * 上传图片文件
     * @param file 上传的文件
     * @return 文件上传响应信息
     */
    FileUploadResponseDTO uploadImage(MultipartFile file);

    /**
     * 删除文件
     * @param fileName 文件名
     * @return 是否删除成功
     */
    boolean deleteFile(String fileName);

    /**
     * 检查文件是否存在
     * @param fileName 文件名
     * @return 是否存在
     */
    boolean fileExists(String fileName);
}
