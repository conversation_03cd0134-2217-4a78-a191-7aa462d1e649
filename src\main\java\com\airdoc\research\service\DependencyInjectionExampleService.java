package com.airdoc.research.service;

import com.airdoc.research.mapper.EmPatientMapper;
import com.airdoc.research.mapper.SysUserMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 依赖注入示例服务类
 * 展示Spring Boot 3推荐的依赖注入方式
 */
@Slf4j
@Service
public class DependencyInjectionExampleService {

    // 方式1: 使用@Resource注解（JSR-250标准，Spring Boot 3推荐）
    // @Resource默认按名称注入，如果找不到匹配的名称，则按类型注入
    @Resource
    private EmPatientMapper patientMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 示例方法：展示@Resource注入的依赖使用
     */
    public void demonstrateResourceInjection() {
        log.info("使用@Resource注入的依赖:");
        log.info("PatientMapper: {}", patientMapper != null ? "已注入" : "未注入");
        log.info("UserMapper: {}", userMapper != null ? "已注入" : "未注入");
        log.info("ObjectMapper: {}", objectMapper != null ? "已注入" : "未注入");
    }

    /**
     * 获取注入状态信息
     */
    public String getInjectionStatus() {
        return String.format("@Resource注入状态 - PatientMapper: %s, UserMapper: %s, ObjectMapper: %s",
                patientMapper != null ? "✓" : "✗",
                userMapper != null ? "✓" : "✗",
                objectMapper != null ? "✓" : "✗");
    }
}
