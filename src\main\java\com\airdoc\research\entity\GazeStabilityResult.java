package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 注视稳定性检测结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gaze_stability_results")
public class GazeStabilityResult extends BaseEntity {

    /**
     * 结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 目标点x坐标(0-1)
     */
    private BigDecimal targetX;

    /**
     * 目标点y坐标(0-1)
     */
    private BigDecimal targetY;

    /**
     * 视线轨迹JSON数据
     */
    private String gazeTrajectoryJson;

    /**
     * 稳定性评分
     */
    private BigDecimal stabilityScore;

    /**
     * 评估结果
     */
    private String evaluationResult;

    /**
     * x轴平均偏移
     */
    private BigDecimal deviationX;

    /**
     * y轴平均偏移
     */
    private BigDecimal deviationY;

    /**
     * 注视总时长(ms)
     */
    private Integer fixationDuration;

    /**
     * 有效注视点数量
     */
    private Integer fixationCount;

    /**
     * 平均距离(cm)
     */
    private BigDecimal averageDistance;

    /**
     * 最大偏移距离
     */
    private BigDecimal maxDeviation;

    /**
     * 均方根误差
     */
    private BigDecimal rmsError;

    /**
     * 目标点半径(px)
     */
    private Integer targetPointRadius;

    /**
     * 视点半径(px)
     */
    private Integer gazePointRadius;

    /**
     * 环半径增长值(px)
     */
    private Integer loopRadiusIncreases;

    /**
     * 环数
     */
    private Integer loopsCount;
}
