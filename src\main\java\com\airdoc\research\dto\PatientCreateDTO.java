package com.airdoc.research.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

// import java.time.LocalDate; // Removed as birthday is now String

/**
 * 患者创建DTO
 */
@Data
public class PatientCreateDTO {

    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空")
    private String name;

    /**
     * 住院号
     */
    private String inpatientNum;

    /**
     * 病例卡号
     */
    private String caseCardNum;

    /**
     * 出生日期 (format: yyyy-MM-dd or other agreed format)
     */
    private String birthday; // Changed from LocalDate to String

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别 1=男 2=女
     */
    private Integer gender;

    /**
     * 患者类型 1=阳性 2=阴性
     */
    private Integer patientType;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    // Removed phoneCountryCode
    // Removed email and @Email annotation
    // Removed avatar
    // Removed description

    /**
     * 诊断信息
     */
    private String diagnosisInformation;

    // Removed organizationId
    // Removed thirdBizId
}
