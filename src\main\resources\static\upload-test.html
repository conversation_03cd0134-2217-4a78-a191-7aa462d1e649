<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .upload-section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="file"], input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .preview {
            margin-top: 15px;
            text-align: center;
        }
        .preview img {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片上传测试页面</h1>
        
        <!-- 通用图片上传 -->
        <div class="upload-section">
            <h2>1. 通用图片上传</h2>
            <form id="generalUploadForm">
                <div class="form-group">
                    <label for="generalFile">选择图片文件：</label>
                    <input type="file" id="generalFile" name="file" accept="image/*" required>
                </div>
                <button type="submit">上传图片</button>
            </form>
            <div id="generalResult"></div>
        </div>

        <!-- 检测记录图片上传 -->
        <div class="upload-section">
            <h2>2. 检测记录图片上传</h2>
            <form id="testRecordUploadForm">
                <div class="form-group">
                    <label for="recordId">检测记录ID：</label>
                    <input type="number" id="recordId" name="recordId" placeholder="请输入检测记录ID" required>
                </div>
                <div class="form-group">
                    <label for="testRecordFile">选择图片文件：</label>
                    <input type="file" id="testRecordFile" name="file" accept="image/*" required>
                </div>
                <button type="submit">上传并关联到检测记录</button>
            </form>
            <div id="testRecordResult"></div>
        </div>

        <!-- 检测结果提交（包含图片URL） -->
        <div class="upload-section">
            <h2>3. 检测结果提交测试（包含图片URL）</h2>
            <form id="movementTestForm">
                <div class="form-group">
                    <label for="patientId">患者ID：</label>
                    <input type="number" id="patientId" name="patientId" placeholder="请输入患者ID" required>
                </div>
                <div class="form-group">
                    <label for="deviceSn">设备序列号：</label>
                    <input type="text" id="deviceSn" name="deviceSn" placeholder="请输入设备序列号" required>
                </div>
                <div class="form-group">
                    <label for="testType">检测类型：</label>
                    <select id="testType" name="testType" required>
                        <option value="GAZE_STABILITY">注视稳定性</option>
                        <option value="FOLLOW_ABILITY">追随能力</option>
                        <option value="SACCADE_ABILITY">扫视能力</option>
                        <option value="ROI_DETECTION">ROI检测</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="imageUrlInput">图片URL（可选）：</label>
                    <input type="url" id="imageUrlInput" name="imageUrl" placeholder="请输入图片URL或先上传图片获取URL">
                </div>
                <div class="form-group">
                    <label for="movementTestFile">或选择图片文件（将自动上传并获取URL）：</label>
                    <input type="file" id="movementTestFile" name="file" accept="image/*">
                </div>
                <button type="submit">提交检测结果</button>
            </form>
            <div id="movementTestResult"></div>
        </div>
    </div>

    <script>
        // 通用图片上传
        document.getElementById('generalUploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('generalFile');
            const resultDiv = document.getElementById('generalResult');
            const submitButton = e.target.querySelector('button[type="submit"]');
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            submitButton.disabled = true;
            submitButton.textContent = '上传中...';
            
            try {
                const response = await fetch('/api/files/upload/image', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'success');
                    showImagePreview(resultDiv, result.data.url);
                } else {
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult(resultDiv, '上传失败: ' + error.message, 'error');
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = '上传图片';
            }
        });

        // 检测记录图片上传
        document.getElementById('testRecordUploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const recordIdInput = document.getElementById('recordId');
            const fileInput = document.getElementById('testRecordFile');
            const resultDiv = document.getElementById('testRecordResult');
            const submitButton = e.target.querySelector('button[type="submit"]');
            
            if (!recordIdInput.value) {
                showResult(resultDiv, '请输入检测记录ID', 'error');
                return;
            }
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, '请选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            submitButton.disabled = true;
            submitButton.textContent = '上传中...';
            
            try {
                const response = await fetch(`/api/files/upload/image/test-record/${recordIdInput.value}`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'success');
                    showImagePreview(resultDiv, result.data.url);
                } else {
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult(resultDiv, '上传失败: ' + error.message, 'error');
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = '上传并关联到检测记录';
            }
        });

        function showResult(resultDiv, message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 检测结果提交（包含图片URL）
        document.getElementById('movementTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const patientIdInput = document.getElementById('patientId');
            const deviceSnInput = document.getElementById('deviceSn');
            const testTypeInput = document.getElementById('testType');
            const imageUrlInput = document.getElementById('imageUrlInput');
            const fileInput = document.getElementById('movementTestFile');
            const resultDiv = document.getElementById('movementTestResult');
            const submitButton = e.target.querySelector('button[type="submit"]');

            if (!patientIdInput.value || !deviceSnInput.value || !testTypeInput.value) {
                showResult(resultDiv, '请填写必填字段', 'error');
                return;
            }

            submitButton.disabled = true;
            submitButton.textContent = '提交中...';

            try {
                let imageUrl = imageUrlInput.value;

                // 如果选择了文件但没有输入URL，先上传文件
                if (fileInput.files[0] && !imageUrl) {
                    const uploadFormData = new FormData();
                    uploadFormData.append('file', fileInput.files[0]);

                    const uploadResponse = await fetch('/api/files/upload/image', {
                        method: 'POST',
                        body: uploadFormData
                    });

                    const uploadResult = await uploadResponse.json();
                    if (uploadResult.code === 200) {
                        imageUrl = uploadResult.data.url;
                        showResult(resultDiv, `图片上传成功，URL: ${imageUrl}`, 'success');
                    } else {
                        throw new Error('图片上传失败: ' + uploadResult.message);
                    }
                }

                // 构建检测结果数据
                const testSequenceMap = {
                    'GAZE_STABILITY': '01',
                    'FOLLOW_ABILITY': '02',
                    'SACCADE_ABILITY': '03',
                    'ROI_DETECTION': '04'
                };

                const submitData = {
                    patientId: parseInt(patientIdInput.value),
                    testInfo: {
                        testType: testTypeInput.value,
                        testSequence: testSequenceMap[testTypeInput.value],
                        testDate: new Date().toISOString().slice(0, 19),
                        duration: 30,
                        notes: '测试提交',
                        imageUrl: imageUrl || null
                    },
                    resultData: {
                        gazeTrajectory: [{
                            index: 1,
                            x: 100.5,
                            y: 200.3,
                            distance: 50.0,
                            duration: 100,
                            timestamp: Date.now(),
                            isValid: true,
                            skew: false
                        }],
                        gazeStabilityData: testTypeInput.value === 'GAZE_STABILITY' ? {
                            targetX: 100.0,
                            targetY: 200.0,
                            stabilityScore: 85.5,
                            evaluationResult: 'GOOD'
                        } : null,
                        followAbilityData: testTypeInput.value === 'FOLLOW_ABILITY' ? {
                            trackingAccuracy: 92.5,
                            averageError: 3.2,
                            reactionTime: 150.0
                        } : null,
                        saccadeAbilityData: testTypeInput.value === 'SACCADE_ABILITY' ? {
                            totalSaccades: 20,
                            successfulSaccades: 18,
                            accuracyRate: 90.0
                        } : null,
                        roiDetectionData: testTypeInput.value === 'ROI_DETECTION' ? {
                            imageWidth: 1920,
                            imageHeight: 1080,
                            fixationCount: 25
                        } : null
                    }
                };

                // 提交检测结果
                const response = await fetch('/api/movement/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Device-Sn': deviceSnInput.value
                    },
                    body: JSON.stringify(submitData)
                });

                const result = await response.json();

                if (result.code === 200) {
                    showResult(resultDiv, `检测结果提交成功！\n记录ID: ${result.data}\n${imageUrl ? '图片URL: ' + imageUrl : ''}`, 'success');
                } else {
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult(resultDiv, '提交失败: ' + error.message, 'error');
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = '提交检测结果';
            }
        });

        function showResult(resultDiv, message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function showImagePreview(resultDiv, imageUrl) {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'preview';
            previewDiv.innerHTML = `
                <p>图片预览：</p>
                <img src="${imageUrl}" alt="上传的图片" onerror="this.style.display='none'">
                <p><a href="${imageUrl}" target="_blank">在新窗口中查看</a></p>
            `;
            resultDiv.appendChild(previewDiv);
        }
    </script>
</body>
</html>
