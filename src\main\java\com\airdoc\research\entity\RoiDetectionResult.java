package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * ROI检测结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("roi_detection_results")
public class RoiDetectionResult extends BaseEntity {

    /**
     * 结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 检测记录ID
     */
    private Long recordId;

    /**
     * 测试图像路径
     */
    private String imagePath;

    /**
     * 图像宽度(px)
     */
    private Integer imageWidth;

    /**
     * 图像高度(px)
     */
    private Integer imageHeight;

    /**
     * 兴趣区域定义
     */
    private String roiRegions;

    /**
     * 视线轨迹JSON数据
     */
    private String gazeTrajectoryJson;

    /**
     * 热力图数据
     */
    private String heatmapData;

    /**
     * 注意力分布统计
     */
    private String attentionDistribution;

    /**
     * 扫描路径数据
     */
    private String scanPath;

    /**
     * 平均注视时长(ms)
     */
    private BigDecimal fixationDurationAvg;

    /**
     * 注视点总数
     */
    private Integer fixationCount;

    /**
     * 扫视次数
     */
    private Integer saccadeCount;

    /**
     * 总观看时间(ms)
     */
    private Integer totalViewingTime;

    /**
     * ROI覆盖率(%)
     */
    private BigDecimal roiCoverageRate;

    /**
     * 各ROI停留时间统计
     */
    private String roiDwellTime;

    /**
     * 首次注视时间(ms)
     */
    private BigDecimal firstFixationTime;

    /**
     * 扫描模式类型
     */
    private String scanPatternType;
}
