package com.airdoc.research.service.impl;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.dto.*;
import com.airdoc.research.entity.*;
import com.airdoc.research.mapper.*;
import com.airdoc.research.service.MovementTestService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;
/**
 * Movement检测服务实现类
 */
@Slf4j
@Service
@Primary // 添加此注解
@RequiredArgsConstructor
public class MovementTestServiceImpl implements MovementTestService {

    private final SysDeviceMapper deviceMapper;
    private final EmTestRecordMapper testRecordMapper;
    private final EmPatientMapper patientMapper;
    private final SysUserMapper userMapper;
    private final GazeTrajectoryDataMapper trajectoryDataMapper;
    private final GazeStabilityResultMapper gazeStabilityResultMapper;
    private final FollowAbilityResultMapper followAbilityResultMapper;
    private final SaccadeAbilityResultMapper saccadeAbilityResultMapper;
    private final RoiDetectionResultMapper roiDetectionResultMapper;

    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitTestResult(MovementTestSubmitDTO submitDTO, String deviceSn) {
        log.info("开始处理Movement检测结果提交，检测类型：{}", submitDTO.getTestInfo().getTestType());
        
        try {
            // 1. 获取患者ID
            Long patientId = submitDTO.getPatientId();

            // 2. 处理设备信息（通过设备序列号）
            Long deviceId = handleDeviceInfo(deviceSn);

            // 3. 创建检测记录（不需要操作员ID）
            Long recordId = createTestRecord(submitDTO.getTestInfo(), patientId, null, deviceId);
            
            // 5. 保存视线轨迹数据
            saveGazeTrajectoryData(recordId, submitDTO.getResultData().getGazeTrajectory());
            
            // 6. 根据检测类型保存对应的结果数据
            saveTestResultData(recordId, submitDTO.getTestInfo().getTestType(), submitDTO.getResultData());
            
            log.info("Movement检测结果提交成功，记录ID：{}", recordId);
            return recordId;
            
        } catch (Exception e) {
            log.error("Movement检测结果提交失败", e);
            throw new RuntimeException("检测结果提交失败：" + e.getMessage());
        }
    }





    /**
     * 处理设备信息
     */
    private Long handleDeviceInfo(String deviceSn) {
        // 根据设备序列号查询设备
        SysDevice existingDevice = deviceMapper.selectOne(
            new LambdaQueryWrapper<SysDevice>()
                .eq(SysDevice::getDeviceSn, deviceSn)
        );

        if (existingDevice != null) {
            // 设备已存在，直接返回ID
            return existingDevice.getId();
        } else {
            // 创建新设备（只有基本信息）
            SysDevice newDevice = new SysDevice();
            newDevice.setDeviceSn(deviceSn);
            newDevice.setDeviceStatus("ONLINE");

            deviceMapper.insert(newDevice);
            return newDevice.getId();
        }
    }

    /**
     * 创建检测记录
     */
    private Long createTestRecord(MovementTestSubmitDTO.TestInfo testInfo, Long patientId, Long operatorId, Long deviceId) {
        EmTestRecord testRecord = new EmTestRecord();
        BeanUtils.copyProperties(testInfo, testRecord);

        testRecord.setPatientId(patientId);
        testRecord.setOperatorId(operatorId);
        testRecord.setDeviceId(deviceId);
        testRecord.setStatus("COMPLETED");

        // 设置图片URL（如果提供了的话）
        if (testInfo.getImageUrl() != null && !testInfo.getImageUrl().trim().isEmpty()) {
            testRecord.setImageUrl(testInfo.getImageUrl());
            log.info("设置检测记录图片URL：{}", testInfo.getImageUrl());
        }

        testRecordMapper.insert(testRecord);
        return testRecord.getId();
    }

    /**
     * 保存视线轨迹数据
     */
    private void saveGazeTrajectoryData(Long recordId, List<MovementTestSubmitDTO.GazePoint> gazeTrajectory) {
        if (gazeTrajectory == null || gazeTrajectory.isEmpty()) {
            return;
        }
        
        for (MovementTestSubmitDTO.GazePoint gazePoint : gazeTrajectory) {
            GazeTrajectoryData trajectoryData = new GazeTrajectoryData();
            BeanUtils.copyProperties(gazePoint, trajectoryData);
            trajectoryData.setRecordId(recordId);
            
            // 将原始数据转换为JSON
            try {
                trajectoryData.setRawData(objectMapper.writeValueAsString(gazePoint));
            } catch (JsonProcessingException e) {
                log.warn("视线轨迹数据JSON转换失败", e);
            }
            
            trajectoryDataMapper.insert(trajectoryData);
        }
    }

    /**
     * 根据检测类型保存对应的结果数据
     */
    private void saveTestResultData(Long recordId, String testType, MovementTestSubmitDTO.TestResultData resultData) {
        switch (testType) {
            case "GAZE_STABILITY":
                saveGazeStabilityResult(recordId, resultData.getGazeStabilityData());
                break;
            case "FOLLOW_ABILITY":
                saveFollowAbilityResult(recordId, resultData.getFollowAbilityData());
                break;
            case "SACCADE_ABILITY":
                saveSaccadeAbilityResult(recordId, resultData.getSaccadeAbilityData());
                break;
            case "ROI_DETECTION":
                saveRoiDetectionResult(recordId, resultData.getRoiDetectionData());
                break;
            default:
                log.warn("未知的检测类型：{}", testType);
        }
    }

    /**
     * 保存注视稳定性结果
     */
    private void saveGazeStabilityResult(Long recordId, MovementTestSubmitDTO.GazeStabilityData data) {
        if (data == null) return;
        
        GazeStabilityResult result = new GazeStabilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);
        
        gazeStabilityResultMapper.insert(result);
    }

    /**
     * 保存追随能力结果
     */
    private void saveFollowAbilityResult(Long recordId, MovementTestSubmitDTO.FollowAbilityData data) {
        if (data == null) return;
        
        FollowAbilityResult result = new FollowAbilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);
        
        followAbilityResultMapper.insert(result);
    }

    /**
     * 保存扫视能力结果
     */
    private void saveSaccadeAbilityResult(Long recordId, MovementTestSubmitDTO.SaccadeAbilityData data) {
        if (data == null) return;
        
        SaccadeAbilityResult result = new SaccadeAbilityResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);
        
        saccadeAbilityResultMapper.insert(result);
    }

    /**
     * 保存ROI检测结果
     */
    private void saveRoiDetectionResult(Long recordId, MovementTestSubmitDTO.RoiDetectionData data) {
        if (data == null) return;

        RoiDetectionResult result = new RoiDetectionResult();
        BeanUtils.copyProperties(data, result);
        result.setRecordId(recordId);

        roiDetectionResultMapper.insert(result);
    }

    // ========== 查询方法实现 ==========

    @Override
    public PageResponse<GazeStabilityResponseDTO> pageQueryGazeStabilityTests(GazeStabilityPageQueryDTO queryDTO) {
        log.debug("分页查询注视稳定性测试，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());

        // 验证分页参数
        queryDTO.validate();

        // 构建查询条件
        LambdaQueryWrapper<GazeStabilityResult> queryWrapper = buildGazeStabilityQueryWrapper(queryDTO);

        // 查询总数
        Long total = gazeStabilityResultMapper.selectCount(queryWrapper);

        if (total == 0) {
            return PageResponse.empty(queryDTO.getCurrent(), queryDTO.getSize());
        }

        // 添加排序
        addGazeStabilityOrderBy(queryWrapper, queryDTO);

        // 分页查询
        List<GazeStabilityResult> results = gazeStabilityResultMapper.selectList(queryWrapper
            .last("LIMIT " + queryDTO.getOffset() + ", " + queryDTO.getSize()));

        // 转换为响应DTO
        List<GazeStabilityResponseDTO> responseDTOs = results.stream()
            .map(this::convertToGazeStabilityResponseDTO)
            .collect(Collectors.toList());

        return PageResponse.of(queryDTO.getCurrent(), queryDTO.getSize(), total, responseDTOs);
    }

    @Override
    public List<GazeStabilityResponseDTO> queryGazeStabilityTests(GazeStabilityQueryDTO queryDTO) {
        log.debug("查询注视稳定性测试列表");

        // 构建查询条件
        LambdaQueryWrapper<GazeStabilityResult> queryWrapper = buildGazeStabilityQueryWrapper(queryDTO);

        // 查询结果列表
        List<GazeStabilityResult> results = gazeStabilityResultMapper.selectList(queryWrapper);

        // 转换为响应DTO
        return results.stream()
            .map(this::convertToGazeStabilityResponseDTO)
            .collect(Collectors.toList());
    }

    @Override
    public GazeStabilityResponseDTO getGazeStabilityTestById(Long id) {
        log.debug("根据ID查询注视稳定性测试详情：{}", id);

        GazeStabilityResult result = gazeStabilityResultMapper.selectById(id);
        if (result == null) {
            throw new RuntimeException("注视稳定性测试结果不存在，ID：" + id);
        }

        return convertToGazeStabilityResponseDTO(result);
    }

    @Override
    public GazeStabilityResponseDTO getGazeStabilityTestByRecordId(Long recordId) {
        log.debug("根据记录ID查询注视稳定性测试详情：{}", recordId);

        GazeStabilityResult result = gazeStabilityResultMapper.selectOne(
            Wrappers.<GazeStabilityResult>lambdaQuery()
                .eq(GazeStabilityResult::getRecordId, recordId)
        );

        if (result == null) {
            throw new RuntimeException("注视稳定性测试结果不存在，记录ID：" + recordId);
        }

        return convertToGazeStabilityResponseDTO(result);
    }

    @Override
    public Long countGazeStabilityTests(GazeStabilityQueryDTO queryDTO) {
        log.debug("统计注视稳定性测试数量");

        // 构建查询条件
        LambdaQueryWrapper<GazeStabilityResult> queryWrapper = buildGazeStabilityQueryWrapper(queryDTO);

        return gazeStabilityResultMapper.selectCount(queryWrapper);
    }

    // ========== 辅助方法 ==========

    /**
     * 构建注视稳定性查询条件
     */
    private LambdaQueryWrapper<GazeStabilityResult> buildGazeStabilityQueryWrapper(Object queryDTO) {
        LambdaQueryWrapper<GazeStabilityResult> wrapper = Wrappers.lambdaQuery();

        // 基础查询条件
        if (queryDTO instanceof GazeStabilityQueryDTO) {
            GazeStabilityQueryDTO dto = (GazeStabilityQueryDTO) queryDTO;
            buildBaseGazeStabilityQuery(wrapper, dto);
        } else if (queryDTO instanceof GazeStabilityPageQueryDTO) {
            GazeStabilityPageQueryDTO dto = (GazeStabilityPageQueryDTO) queryDTO;
            buildBaseGazeStabilityQuery(wrapper, dto);
        }

        return wrapper;
    }

    /**
     * 构建基础查询条件
     */
    private void buildBaseGazeStabilityQuery(LambdaQueryWrapper<GazeStabilityResult> wrapper, Object queryDTO) {
        // 由于查询条件涉及多表关联，这里先实现基本的单表查询
        // 后续可以通过自定义SQL或者分步查询来实现复杂条件

        if (queryDTO instanceof GazeStabilityQueryDTO) {
            GazeStabilityQueryDTO dto = (GazeStabilityQueryDTO) queryDTO;

            // 评估结果
            if (StringUtils.hasText(dto.getEvaluationResult())) {
                wrapper.eq(GazeStabilityResult::getEvaluationResult, dto.getEvaluationResult());
            }

            // 稳定性评分范围
            if (dto.getMinStabilityScore() != null) {
                wrapper.ge(GazeStabilityResult::getStabilityScore, dto.getMinStabilityScore());
            }
            if (dto.getMaxStabilityScore() != null) {
                wrapper.le(GazeStabilityResult::getStabilityScore, dto.getMaxStabilityScore());
            }

            // 注视时长范围
            if (dto.getMinFixationDuration() != null) {
                wrapper.ge(GazeStabilityResult::getFixationDuration, dto.getMinFixationDuration());
            }
            if (dto.getMaxFixationDuration() != null) {
                wrapper.le(GazeStabilityResult::getFixationDuration, dto.getMaxFixationDuration());
            }

            // 注视点数量范围
            if (dto.getMinFixationCount() != null) {
                wrapper.ge(GazeStabilityResult::getFixationCount, dto.getMinFixationCount());
            }
            if (dto.getMaxFixationCount() != null) {
                wrapper.le(GazeStabilityResult::getFixationCount, dto.getMaxFixationCount());
            }

            // 平均距离范围
            if (dto.getMinAverageDistance() != null) {
                wrapper.ge(GazeStabilityResult::getAverageDistance, dto.getMinAverageDistance());
            }
            if (dto.getMaxAverageDistance() != null) {
                wrapper.le(GazeStabilityResult::getAverageDistance, dto.getMaxAverageDistance());
            }

            // 最大偏移范围
            if (dto.getMinMaxDeviation() != null) {
                wrapper.ge(GazeStabilityResult::getMaxDeviation, dto.getMinMaxDeviation());
            }
            if (dto.getMaxMaxDeviation() != null) {
                wrapper.le(GazeStabilityResult::getMaxDeviation, dto.getMaxMaxDeviation());
            }

            // RMS误差范围
            if (dto.getMinRmsError() != null) {
                wrapper.ge(GazeStabilityResult::getRmsError, dto.getMinRmsError());
            }
            if (dto.getMaxRmsError() != null) {
                wrapper.le(GazeStabilityResult::getRmsError, dto.getMaxRmsError());
            }

            // 创建时间范围
            if (dto.getCreatedAtStart() != null) {
                wrapper.ge(GazeStabilityResult::getCreatedAt, dto.getCreatedAtStart());
            }
            if (dto.getCreatedAtEnd() != null) {
                wrapper.le(GazeStabilityResult::getCreatedAt, dto.getCreatedAtEnd());
            }
        } else if (queryDTO instanceof GazeStabilityPageQueryDTO) {
            GazeStabilityPageQueryDTO dto = (GazeStabilityPageQueryDTO) queryDTO;

            // 评估结果
            if (StringUtils.hasText(dto.getEvaluationResult())) {
                wrapper.eq(GazeStabilityResult::getEvaluationResult, dto.getEvaluationResult());
            }

            // 稳定性评分范围
            if (dto.getMinStabilityScore() != null) {
                wrapper.ge(GazeStabilityResult::getStabilityScore, dto.getMinStabilityScore());
            }
            if (dto.getMaxStabilityScore() != null) {
                wrapper.le(GazeStabilityResult::getStabilityScore, dto.getMaxStabilityScore());
            }

            // 其他条件类似处理...
        }
    }

    /**
     * 添加排序条件
     */
    private void addGazeStabilityOrderBy(LambdaQueryWrapper<GazeStabilityResult> wrapper, GazeStabilityPageQueryDTO queryDTO) {
        String sortField = queryDTO.getSortField();
        String sortOrder = queryDTO.getSortOrder();

        if (StringUtils.hasText(sortField)) {
            boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);

            switch (sortField) {
                case "stabilityScore":
                    wrapper.orderBy(true, isAsc, GazeStabilityResult::getStabilityScore);
                    break;
                case "fixationDuration":
                    wrapper.orderBy(true, isAsc, GazeStabilityResult::getFixationDuration);
                    break;
                case "fixationCount":
                    wrapper.orderBy(true, isAsc, GazeStabilityResult::getFixationCount);
                    break;
                case "averageDistance":
                    wrapper.orderBy(true, isAsc, GazeStabilityResult::getAverageDistance);
                    break;
                case "createdAt":
                    wrapper.orderBy(true, isAsc, GazeStabilityResult::getCreatedAt);
                    break;
                default:
                    // 默认按创建时间倒序
                    wrapper.orderByDesc(GazeStabilityResult::getCreatedAt);
            }
        } else {
            // 默认按创建时间倒序
            wrapper.orderByDesc(GazeStabilityResult::getCreatedAt);
        }
    }

    /**
     * 转换为注视稳定性响应DTO
     */
    private GazeStabilityResponseDTO convertToGazeStabilityResponseDTO(GazeStabilityResult result) {
        GazeStabilityResponseDTO responseDTO = new GazeStabilityResponseDTO();
        BeanUtils.copyProperties(result, responseDTO);

        // 查询关联的检测记录信息
        EmTestRecord testRecord = testRecordMapper.selectById(result.getRecordId());
        if (testRecord != null) {
            responseDTO.setRecordId(testRecord.getId());
            responseDTO.setPatientId(testRecord.getPatientId());
            responseDTO.setDeviceId(testRecord.getDeviceId());
            responseDTO.setOperatorId(testRecord.getOperatorId());
            responseDTO.setTestType(testRecord.getTestType());
            responseDTO.setTestSequence(testRecord.getTestSequence());
            responseDTO.setTestDate(testRecord.getTestDate());
            responseDTO.setDuration(testRecord.getDuration());
            responseDTO.setStatus(testRecord.getStatus());
            responseDTO.setStatusDesc(getStatusDescription(testRecord.getStatus()));
            responseDTO.setCalibrationParams(testRecord.getCalibrationParams());
            responseDTO.setEnvironmentInfo(testRecord.getEnvironmentInfo());
            responseDTO.setNotes(testRecord.getNotes());
            responseDTO.setImageUrl(testRecord.getImageUrl());

            // 查询患者信息
            if (testRecord.getPatientId() != null) {
                EmPatient patient = patientMapper.selectById(testRecord.getPatientId());
                if (patient != null) {
                    responseDTO.setPatientName(patient.getName());
                    responseDTO.setInpatientNum(patient.getInpatientNum());
                    responseDTO.setCaseCardNum(patient.getCaseCardNum());
                }
            }

            // 查询设备信息
            if (testRecord.getDeviceId() != null) {
                SysDevice device = deviceMapper.selectById(testRecord.getDeviceId());
                if (device != null) {
                    responseDTO.setDeviceName(device.getDeviceName());
                    responseDTO.setDeviceSn(device.getDeviceSn());
                }
            }

            // 查询操作员信息
            if (testRecord.getOperatorId() != null) {
                SysUser operator = userMapper.selectById(testRecord.getOperatorId());
                if (operator != null) {
                    responseDTO.setOperatorName(operator.getUsername());
                }
            }
        }

        return responseDTO;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        if (status == null) return null;

        switch (status) {
            case "COMPLETED":
                return "已完成";
            case "FAILED":
                return "失败";
            case "INTERRUPTED":
                return "中断";
            default:
                return status;
        }
    }

    // ========== 扫视能力检测查询方法实现 ==========

    @Override
    public PageResponse<SaccadeAbilityResponseDTO> pageQuerySaccadeAbilityTests(SaccadeAbilityPageQueryDTO queryDTO) {
        log.debug("分页查询扫视能力检测，页码：{}，页大小：{}", queryDTO.getCurrent(), queryDTO.getSize());

        // 验证分页参数
        queryDTO.validate();

        // 构建查询条件
        LambdaQueryWrapper<SaccadeAbilityResult> queryWrapper = buildSaccadeAbilityQueryWrapper(queryDTO);

        // 查询总数
        Long total = saccadeAbilityResultMapper.selectCount(queryWrapper);

        if (total == 0) {
            return PageResponse.empty(queryDTO.getCurrent(), queryDTO.getSize());
        }

        // 添加排序
        addSaccadeAbilityOrderBy(queryWrapper, queryDTO);

        // 分页查询
        List<SaccadeAbilityResult> results = saccadeAbilityResultMapper.selectList(queryWrapper
            .last("LIMIT " + queryDTO.getOffset() + ", " + queryDTO.getSize()));

        // 转换为响应DTO
        List<SaccadeAbilityResponseDTO> responseDTOs = results.stream()
            .map(this::convertToSaccadeAbilityResponseDTO)
            .collect(Collectors.toList());

        return PageResponse.of(queryDTO.getCurrent(), queryDTO.getSize(), total, responseDTOs);
    }

    @Override
    public List<SaccadeAbilityResponseDTO> querySaccadeAbilityTests(SaccadeAbilityQueryDTO queryDTO) {
        log.debug("查询扫视能力检测列表");

        // 构建查询条件
        LambdaQueryWrapper<SaccadeAbilityResult> queryWrapper = buildSaccadeAbilityQueryWrapper(queryDTO);

        // 查询结果列表
        List<SaccadeAbilityResult> results = saccadeAbilityResultMapper.selectList(queryWrapper);

        // 转换为响应DTO
        return results.stream()
            .map(this::convertToSaccadeAbilityResponseDTO)
            .collect(Collectors.toList());
    }

    @Override
    public SaccadeAbilityResponseDTO getSaccadeAbilityTestById(Long id) {
        log.debug("根据ID查询扫视能力检测详情：{}", id);

        SaccadeAbilityResult result = saccadeAbilityResultMapper.selectById(id);
        if (result == null) {
            throw new RuntimeException("扫视能力检测结果不存在，ID：" + id);
        }

        return convertToSaccadeAbilityResponseDTO(result);
    }

    @Override
    public SaccadeAbilityResponseDTO getSaccadeAbilityTestByRecordId(Long recordId) {
        log.debug("根据记录ID查询扫视能力检测详情：{}", recordId);

        LambdaQueryWrapper<SaccadeAbilityResult> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SaccadeAbilityResult::getRecordId, recordId);

        SaccadeAbilityResult result = saccadeAbilityResultMapper.selectOne(wrapper);
        if (result == null) {
            throw new RuntimeException("扫视能力检测结果不存在，记录ID：" + recordId);
        }

        return convertToSaccadeAbilityResponseDTO(result);
    }

    @Override
    public Long countSaccadeAbilityTests(SaccadeAbilityQueryDTO queryDTO) {
        log.debug("统计扫视能力检测数量");

        // 构建查询条件
        LambdaQueryWrapper<SaccadeAbilityResult> queryWrapper = buildSaccadeAbilityQueryWrapper(queryDTO);

        return saccadeAbilityResultMapper.selectCount(queryWrapper);
    }

    // ========== 扫视能力检测辅助方法 ==========

    /**
     * 构建扫视能力查询条件
     */
    private LambdaQueryWrapper<SaccadeAbilityResult> buildSaccadeAbilityQueryWrapper(Object queryDTO) {
        LambdaQueryWrapper<SaccadeAbilityResult> wrapper = Wrappers.lambdaQuery();

        // 基础查询条件
        if (queryDTO instanceof SaccadeAbilityQueryDTO) {
            SaccadeAbilityQueryDTO dto = (SaccadeAbilityQueryDTO) queryDTO;
            buildBaseSaccadeAbilityQuery(wrapper, dto);
        } else if (queryDTO instanceof SaccadeAbilityPageQueryDTO) {
            SaccadeAbilityPageQueryDTO dto = (SaccadeAbilityPageQueryDTO) queryDTO;
            buildBaseSaccadeAbilityQuery(wrapper, dto);
        }

        return wrapper;
    }

    /**
     * 构建基础扫视能力查询条件
     */
    private void buildBaseSaccadeAbilityQuery(LambdaQueryWrapper<SaccadeAbilityResult> wrapper, Object queryDTO) {
        if (queryDTO instanceof SaccadeAbilityQueryDTO) {
            SaccadeAbilityQueryDTO dto = (SaccadeAbilityQueryDTO) queryDTO;

            // 准确率范围
            if (dto.getMinAccuracyRate() != null) {
                wrapper.ge(SaccadeAbilityResult::getAccuracyRate, dto.getMinAccuracyRate());
            }
            if (dto.getMaxAccuracyRate() != null) {
                wrapper.le(SaccadeAbilityResult::getAccuracyRate, dto.getMaxAccuracyRate());
            }

            // 总扫视次数范围
            if (dto.getMinTotalSaccades() != null) {
                wrapper.ge(SaccadeAbilityResult::getTotalSaccades, dto.getMinTotalSaccades());
            }
            if (dto.getMaxTotalSaccades() != null) {
                wrapper.le(SaccadeAbilityResult::getTotalSaccades, dto.getMaxTotalSaccades());
            }

            // 成功扫视次数范围
            if (dto.getMinSuccessfulSaccades() != null) {
                wrapper.ge(SaccadeAbilityResult::getSuccessfulSaccades, dto.getMinSuccessfulSaccades());
            }
            if (dto.getMaxSuccessfulSaccades() != null) {
                wrapper.le(SaccadeAbilityResult::getSuccessfulSaccades, dto.getMaxSuccessfulSaccades());
            }

            // 平均扫视时间范围
            if (dto.getMinAverageSaccadeTime() != null) {
                wrapper.ge(SaccadeAbilityResult::getAverageSaccadeTime, dto.getMinAverageSaccadeTime());
            }
            if (dto.getMaxAverageSaccadeTime() != null) {
                wrapper.le(SaccadeAbilityResult::getAverageSaccadeTime, dto.getMaxAverageSaccadeTime());
            }

            // 扫视速度范围
            if (dto.getMinSaccadeVelocity() != null) {
                wrapper.ge(SaccadeAbilityResult::getSaccadeVelocity, dto.getMinSaccadeVelocity());
            }
            if (dto.getMaxSaccadeVelocity() != null) {
                wrapper.le(SaccadeAbilityResult::getSaccadeVelocity, dto.getMaxSaccadeVelocity());
            }

            // 误差距离范围
            if (dto.getMinErrorDistance() != null) {
                wrapper.ge(SaccadeAbilityResult::getErrorDistance, dto.getMinErrorDistance());
            }
            if (dto.getMaxErrorDistance() != null) {
                wrapper.le(SaccadeAbilityResult::getErrorDistance, dto.getMaxErrorDistance());
            }

            // 扫视潜伏期范围
            if (dto.getMinLatency() != null) {
                wrapper.ge(SaccadeAbilityResult::getLatency, dto.getMinLatency());
            }
            if (dto.getMaxLatency() != null) {
                wrapper.le(SaccadeAbilityResult::getLatency, dto.getMaxLatency());
            }

            // 峰值速度范围
            if (dto.getMinPeakVelocity() != null) {
                wrapper.ge(SaccadeAbilityResult::getPeakVelocity, dto.getMinPeakVelocity());
            }
            if (dto.getMaxPeakVelocity() != null) {
                wrapper.le(SaccadeAbilityResult::getPeakVelocity, dto.getMaxPeakVelocity());
            }

            // 欠冲率范围
            if (dto.getMinUndershootRate() != null) {
                wrapper.ge(SaccadeAbilityResult::getUndershootRate, dto.getMinUndershootRate());
            }
            if (dto.getMaxUndershootRate() != null) {
                wrapper.le(SaccadeAbilityResult::getUndershootRate, dto.getMaxUndershootRate());
            }

            // 过冲率范围
            if (dto.getMinOvershootRate() != null) {
                wrapper.ge(SaccadeAbilityResult::getOvershootRate, dto.getMinOvershootRate());
            }
            if (dto.getMaxOvershootRate() != null) {
                wrapper.le(SaccadeAbilityResult::getOvershootRate, dto.getMaxOvershootRate());
            }

            // 注视稳定性评分范围
            if (dto.getMinFixationStability() != null) {
                wrapper.ge(SaccadeAbilityResult::getFixationStability, dto.getMinFixationStability());
            }
            if (dto.getMaxFixationStability() != null) {
                wrapper.le(SaccadeAbilityResult::getFixationStability, dto.getMaxFixationStability());
            }

            // 创建时间范围
            if (dto.getCreatedAtStart() != null) {
                wrapper.ge(SaccadeAbilityResult::getCreatedAt, dto.getCreatedAtStart());
            }
            if (dto.getCreatedAtEnd() != null) {
                wrapper.le(SaccadeAbilityResult::getCreatedAt, dto.getCreatedAtEnd());
            }
        } else if (queryDTO instanceof SaccadeAbilityPageQueryDTO) {
            SaccadeAbilityPageQueryDTO dto = (SaccadeAbilityPageQueryDTO) queryDTO;

            // 准确率范围
            if (dto.getMinAccuracyRate() != null) {
                wrapper.ge(SaccadeAbilityResult::getAccuracyRate, dto.getMinAccuracyRate());
            }
            if (dto.getMaxAccuracyRate() != null) {
                wrapper.le(SaccadeAbilityResult::getAccuracyRate, dto.getMaxAccuracyRate());
            }

            // 总扫视次数范围
            if (dto.getMinTotalSaccades() != null) {
                wrapper.ge(SaccadeAbilityResult::getTotalSaccades, dto.getMinTotalSaccades());
            }
            if (dto.getMaxTotalSaccades() != null) {
                wrapper.le(SaccadeAbilityResult::getTotalSaccades, dto.getMaxTotalSaccades());
            }

            // 其他条件类似处理...
        }
    }

    /**
     * 添加扫视能力排序条件
     */
    private void addSaccadeAbilityOrderBy(LambdaQueryWrapper<SaccadeAbilityResult> wrapper, SaccadeAbilityPageQueryDTO queryDTO) {
        String sortField = queryDTO.getSortField();
        String sortOrder = queryDTO.getSortOrder();

        if (StringUtils.hasText(sortField)) {
            boolean isAsc = "ASC".equalsIgnoreCase(sortOrder);

            switch (sortField) {
                case "accuracyRate":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getAccuracyRate);
                    break;
                case "totalSaccades":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getTotalSaccades);
                    break;
                case "successfulSaccades":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getSuccessfulSaccades);
                    break;
                case "averageSaccadeTime":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getAverageSaccadeTime);
                    break;
                case "saccadeVelocity":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getSaccadeVelocity);
                    break;
                case "errorDistance":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getErrorDistance);
                    break;
                case "createdAt":
                    wrapper.orderBy(true, isAsc, SaccadeAbilityResult::getCreatedAt);
                    break;
                default:
                    // 默认按创建时间倒序
                    wrapper.orderByDesc(SaccadeAbilityResult::getCreatedAt);
            }
        } else {
            // 默认按创建时间倒序
            wrapper.orderByDesc(SaccadeAbilityResult::getCreatedAt);
        }
    }

    /**
     * 转换为扫视能力响应DTO
     */
    private SaccadeAbilityResponseDTO convertToSaccadeAbilityResponseDTO(SaccadeAbilityResult result) {
        SaccadeAbilityResponseDTO responseDTO = new SaccadeAbilityResponseDTO();
        BeanUtils.copyProperties(result, responseDTO);

        // 查询关联的检测记录信息
        EmTestRecord testRecord = testRecordMapper.selectById(result.getRecordId());
        if (testRecord != null) {
            responseDTO.setRecordId(testRecord.getId());
            responseDTO.setPatientId(testRecord.getPatientId());
            responseDTO.setDeviceId(testRecord.getDeviceId());
            responseDTO.setOperatorId(testRecord.getOperatorId());
            responseDTO.setTestType(testRecord.getTestType());
            responseDTO.setTestSequence(testRecord.getTestSequence());
            responseDTO.setTestDate(testRecord.getTestDate());
            responseDTO.setDuration(testRecord.getDuration());
            responseDTO.setStatus(testRecord.getStatus());
            responseDTO.setStatusDesc(getStatusDescription(testRecord.getStatus()));
            responseDTO.setCalibrationParams(testRecord.getCalibrationParams());
            responseDTO.setEnvironmentInfo(testRecord.getEnvironmentInfo());
            responseDTO.setNotes(testRecord.getNotes());
            responseDTO.setImageUrl(testRecord.getImageUrl());

            // 查询患者信息
            if (testRecord.getPatientId() != null) {
                EmPatient patient = patientMapper.selectById(testRecord.getPatientId());
                if (patient != null) {
                    responseDTO.setPatientName(patient.getName());
                    responseDTO.setInpatientNum(patient.getInpatientNum());
                    responseDTO.setCaseCardNum(patient.getCaseCardNum());
                }
            }

            // 查询设备信息
            if (testRecord.getDeviceId() != null) {
                SysDevice device = deviceMapper.selectById(testRecord.getDeviceId());
                if (device != null) {
                    responseDTO.setDeviceName(device.getDeviceName());
                    responseDTO.setDeviceSn(device.getDeviceSn());
                }
            }

            // 查询操作员信息
            if (testRecord.getOperatorId() != null) {
                SysUser operator = userMapper.selectById(testRecord.getOperatorId());
                if (operator != null) {
                    responseDTO.setOperatorName(operator.getUsername());
                }
            }
        }

        return responseDTO;
    }
}
