package com.airdoc.research.common;

import lombok.Data;

import java.util.List;

/**
 * 分页响应结果
 */
@Data
public class PageResponse<T> {

    /**
     * 当前页码
     */
    private Integer current;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    public PageResponse() {
    }

    public PageResponse(Integer current, Integer size, Long total, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        this.pages = (total + size - 1) / size;
        this.hasPrevious = current > 1;
        this.hasNext = current < pages;
    }

    /**
     * 创建分页响应
     */
    public static <T> PageResponse<T> of(Integer current, Integer size, Long total, List<T> records) {
        return new PageResponse<>(current, size, total, records);
    }

    /**
     * 创建空分页响应
     */
    public static <T> PageResponse<T> empty(Integer current, Integer size) {
        return new PageResponse<>(current, size, 0L, List.of());
    }
}
