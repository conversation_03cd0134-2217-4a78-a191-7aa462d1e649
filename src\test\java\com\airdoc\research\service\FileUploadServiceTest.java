//package com.airdoc.research.service;
//
//import com.airdoc.research.dto.FileUploadResponseDTO;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.ActiveProfiles;
//
//import jakarta.annotation.Resource;
//import java.io.IOException;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * 文件上传服务测试类
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class FileUploadServiceTest {
//
//    @Resource
//    private FileUploadService fileUploadService;
//
//    @Test
//    public void testUploadImage() throws IOException {
//        // 创建模拟图片文件
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file",
//                "test-image.jpg",
//                "image/jpeg",
//                "test image content".getBytes()
//        );
//
//        // 测试上传
//        FileUploadResponseDTO result = fileUploadService.uploadImage(mockFile);
//
//        // 验证结果
//        assertNotNull(result);
//        assertNotNull(result.getUrl());
//        assertNotNull(result.getFileName());
//        assertEquals("test-image.jpg", result.getOriginalFileName());
//        assertEquals("image/jpeg", result.getContentType());
//        assertTrue(result.getFileSize() > 0);
//
//        // 清理测试文件
//        fileUploadService.deleteFile(result.getFileName());
//    }
//
//    @Test
//    public void testUploadInvalidFile() {
//        // 创建无效文件类型
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file",
//                "test.txt",
//                "text/plain",
//                "test content".getBytes()
//        );
//
//        // 测试上传应该抛出异常
//        assertThrows(RuntimeException.class, () -> {
//            fileUploadService.uploadImage(mockFile);
//        });
//    }
//
//    @Test
//    public void testFileExists() {
//        // 测试不存在的文件
//        assertFalse(fileUploadService.fileExists("non-existent-file.jpg"));
//    }
//}
