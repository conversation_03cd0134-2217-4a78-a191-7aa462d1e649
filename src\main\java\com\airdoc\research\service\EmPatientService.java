package com.airdoc.research.service;

import com.airdoc.research.common.PageResponse;
import com.airdoc.research.dto.*;
import com.airdoc.research.entity.EmPatient;

import java.util.List;

/**
 * 患者管理服务接口
 */
public interface EmPatientService {

    /**
     * 创建患者
     * @param createDTO 创建患者DTO
     * @return 患者ID响应DTO
     */
    PatientIdResponseDTO createPatient(PatientCreateDTO createDTO);

    /**
     * 根据ID获取患者信息
     * @param id 患者ID
     * @return 患者响应DTO
     */
    PatientResponseDTO getPatientById(Long id);

    /**
     * 根据UUID获取患者信息
     * @param patientUuid 患者UUID
     * @return 患者响应DTO
     */
    PatientResponseDTO getPatientByUuid(String patientUuid);

    /**
     * 更新患者信息
     * @param updateDTO 更新患者DTO
     * @return 患者响应DTO
     */
    PatientResponseDTO updatePatient(PatientUpdateDTO updateDTO);

    /**
     * 删除患者（逻辑删除）
     * @param id 患者ID
     * @return 是否删除成功
     */
    Boolean deletePatient(Long id);

    /**
     * 批量删除患者（逻辑删除）
     * @param ids 患者ID列表
     * @return 删除成功的数量
     */
    Integer batchDeletePatients(List<Long> ids);

    /**
     * 分页查询患者
     * @param queryDTO 分页查询DTO
     * @return 分页响应结果
     */
    PageResponse<PatientResponseDTO> pageQueryPatients(PatientPageQueryDTO queryDTO);

    /**
     * 查询患者列表
     * @param queryDTO 查询DTO
     * @return 患者列表
     */
    List<PatientResponseDTO> queryPatients(PatientQueryDTO queryDTO);

    /**
     * 检查患者是否存在
     * @param id 患者ID
     * @return 是否存在
     */
    Boolean existsById(Long id);

    /**
     * 检查患者UUID是否存在
     * @param patientUuid 患者UUID
     * @return 是否存在
     */
    Boolean existsByUuid(String patientUuid);

    /**
     * 根据住院号查询患者
     * @param inpatientNum 住院号
     * @return 患者响应DTO
     */
    PatientResponseDTO getPatientByInpatientNum(String inpatientNum);

    /**
     * 根据病例卡号查询患者
     * @param caseCardNum 病例卡号
     * @return 患者响应DTO
     */
    PatientResponseDTO getPatientByCaseCardNum(String caseCardNum);

    /**
     * 统计患者总数
     * @return 患者总数
     */
    Long countPatients();

    /**
     * 根据机构ID统计患者数量
     * @param organizationId 机构ID
     * @return 患者数量
     */
    Long countPatientsByOrganization(String organizationId);
}
