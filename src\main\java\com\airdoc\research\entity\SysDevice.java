package com.airdoc.research.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_devices")
public class SysDevice extends BaseEntity {

    /**
     * 设备ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 品牌
     */
    private Integer brand;

    /**
     * 设备环境类型
     */
    private String placementType;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 是否演示模式
     */
    private Boolean isDemoMode;

    /**
     * 是否海外设备
     */
    private Boolean isOverseas;

    /**
     * 是否开启调试
     */
    private Boolean isDebugEnable;

    /**
     * 支持SKU切换
     */
    private Boolean placementTypeSwitch;

    /**
     * IoT配置信息
     */
    private String iotConfig;

    /**
     * 首页logo地址
     */
    private String logo;

    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 设备状态
     */
    private String deviceStatus;
}
